"""
AI Statement Comparison Prompt for Stage 2 Processing.

This module creates prompts for AI-driven comparison of AI corrected output vs applied modules output
to determine if they have similar target functionality.
"""

def create_ai_statement_comparison_prompt(
    ai_corrected_statement: str,
    applied_modules_statement: str,
    db_terms: dict
) -> str:
    """
    Create a simplified prompt for AI-driven statement comparison.

    Args:
        ai_corrected_statement: AI corrected PostgreSQL statement from Stage 1
        applied_modules_statement: Statement after applying updated modules
        db_terms: Database-specific terminology

    Returns:
        str: Simplified comparison prompt for AI analysis
    """

    target_db = db_terms['target_db']
    expert_title = db_terms['expert_title']

    prompt = f"""
You are a SENIOR {expert_title} with expertise in {target_db} statement comparison.

Your task is to compare two {target_db} statements to determine if they are essentially the same with only minor formatting differences.

CORE COMPARISON TASK:
Since both statements are already in {target_db} format, focus on:
1. **STATEMENT SIMILARITY**: Determine if both statements are essentially the same {target_db} code
2. **SYNTAX MATCHING**: Check if the core SQL logic and structure are the same
3. **FORMATTING TOLERANCE**: Allow for minor differences in whitespace, case, and formatting
4. **LOGIC EQUIVALENCE**: Verify both statements implement the same database operations

COMPARISON CONTEXT:
==================
**Target Database**: {target_db}

**AI Corrected {target_db} Statement** (Expected):
{ai_corrected_statement}

**Applied Modules {target_db} Statement** (Actual):
{applied_modules_statement}

COMPARISON CRITERIA:
===================

1. **CORE LOGIC MATCHING**:
   - Do both statements implement the same SQL operations and logic?
   - Are the main database functions and operations equivalent?
   - Do both statements process the same data in the same way?

2. **SQL CLAUSE ORDER VALIDATION**:
   - **CRITICAL**: SQL clause order must be identical for functional equivalence
   - All SQL clauses must appear in the same relative positions between statements
   - Clause positioning affects SQL execution behavior and results

3. **ELEMENTS TO IGNORE COMPLETELY**:
   - All whitespace, tabs, line breaks, and indentation differences
   - All case differences (uppercase vs lowercase)
   - All formatting and visual presentation variations
   - All comment content (-- comments, /* */ comments)
   - All comment markers and processing placeholders
   - All temporary markers used for processing workflows
   - Visual arrangement differences that don't affect SQL execution
   - Syntax formatting that doesn't change functional behavior

4. **CRITICAL DIFFERENCES TO IDENTIFY**:
   - Different SQL functions, operations, or keywords
   - Different data processing logic or algorithms
   - Missing or extra SQL functionality between statements
   - Different result sets or execution outcomes
   - Different positioning of SQL clauses that affects execution sequence
   - Extra or missing SQL statements or blocks
   - Structural differences that impact SQL execution behavior
   - Different clause ordering that changes functional behavior

COMPARISON METHODOLOGY:
======================

**STEP 1: NORMALIZE BOTH STATEMENTS**
- Convert both statements to consistent case (all uppercase or lowercase)
- Remove all whitespace variations (spaces, tabs, line breaks, indentation)
- Remove all comment content (lines starting with --, content within /* */)
- Remove comment marker patterns and processing placeholders
- Extract only the functional SQL tokens in their original sequence
- Result: Two clean token sequences for comparison

**STEP 2: TOKEN-BY-TOKEN COMPARISON**
- Compare the normalized token sequences element by element
- Identify tokens that are missing, extra, or in different positions
- Map each SQL keyword, function, and clause to its position in each statement
- Create a detailed difference list showing position mismatches
- Focus on SQL structure, not formatting differences

**STEP 3: SQL CLAUSE STRUCTURE ANALYSIS**
- Identify all SQL clauses in both statements (SELECT, FROM, WHERE, ORDER BY, LIMIT, etc.)
- Compare the sequential order of clauses between statements
- Check if clauses appear in the same relative positions
- Identify any clauses that are missing, extra, or repositioned
- Verify that clause ordering follows proper SQL execution sequence

**STEP 4: KEYWORD AND FUNCTION ANALYSIS**
- Compare SQL keywords and functions used in both statements
- Identify missing, extra, or different keywords that affect functionality
- Check for variations in SQL operations that change behavior
- Analyze function calls and their parameters for differences
- Focus on elements that impact SQL execution results

**STEP 5: COMPREHENSIVE DIFFERENCE REPORTING**
- List every structural difference found between normalized statements
- Specify exact SQL elements that differ in position or presence
- Explain how each difference impacts functional equivalence
- Categorize differences by their effect on SQL execution behavior
- Provide actionable feedback for addressing each difference

COMPARISON GUIDELINES:
=====================

**EQUIVALENT STATEMENTS** must have:
- Same core SQL operations and logic
- Same data processing approach
- **CRITICAL**: Identical element sequence and positioning
- **CRITICAL**: Same relative order of all components
- **NOTE**: Only whitespace, formatting, and case differences are ignored

**NON-EQUIVALENT STATEMENTS** have:
- Different SQL operations or functions
- Different data processing logic
- **CRITICAL**: Different element ordering or positioning
- **CRITICAL**: Different sequence of components (even if same components exist)
- Missing or extra functionality

DECISION CRITERIA:
=================

**STATEMENTS MATCH** (return true):
✅ Both normalized statements implement identical SQL logic and operations
✅ Core functionality is equivalent after removing formatting differences
✅ Same data processing approach and result structure
✅ Identical sequential order of all SQL elements after normalization
✅ Same relative positioning of all functional components
✅ Only formatting, whitespace, case, and empty comments differ between statements

**STATEMENTS DON'T MATCH** (return false):
❌ Different SQL operations, functions, or keywords after normalization
❌ Missing or extra SQL functionality between normalized statements
❌ Different data processing approaches that yield different results
❌ Different positioning or ordering of SQL clauses that affects execution
❌ Structural differences that impact SQL execution behavior
❌ Different sequence of functional SQL components

OUTPUT FORMAT (JSON):
====================
{{
  "statements_match": <boolean - true if normalized statements are functionally equivalent, false if they have structural or functional differences>,
  "explanation": "<systematic analysis following this format: 1) List all SQL clause positioning differences, 2) List all missing or extra keywords/functions, 3) List all structural variations that affect execution, 4) Ignore all comments and formatting differences, 5) Provide specific actionable differences for module enhancement>",
  "transformation_guidance": {{
    "required": <boolean - true if transformation is needed>,
    "specific_changes": [
      {{
        "source_pattern": "<Current pattern that needs to change>",
        "target_pattern": "<What it should become>",
        "transformation_method": "<How to implement the change>",
        "variations_to_handle": ["<variation1>", "<variation2>", "<variation3>"]
      }}
    ],
    "implementation_steps": [
      "<Step 1: Specific action>",
      "<Step 2: Specific action>",
      "<Step 3: Specific action>"
    ],
    "parameter_mapping": {{
      "extraction": "<How to extract variable components>",
      "reconstruction": "<How to reconstruct in target format>"
    }}
  }}
}}

COMPARISON FOCUS:
================
- **Normalization First**: Always normalize both statements by removing whitespace/case differences and comments
- **Core Logic**: Compare normalized statements for same SQL operations
- **Clause Order**: Verify clause ordering in normalized versions
- **Functional Equivalence**: Focus on whether normalized statements would produce the same results

NORMALIZATION EXAMPLES:
======================
Before normalization:
Statement A: "SELECT col1 FROM table WHERE id = 1 ORDER BY col1 LIMIT 5;"
Statement B: "select col1 from table where id = 1 limit 5 order by col1;"

After normalization (remove case/whitespace):
Statement A: "SELECT COL1 FROM TABLE WHERE ID = 1 ORDER BY COL1 LIMIT 5"
Statement B: "SELECT COL1 FROM TABLE WHERE ID = 1 LIMIT 5 ORDER BY COL1"

Analysis: Different clause positioning (ORDER BY vs LIMIT sequence) - NOT EQUIVALENT

Comment marker patterns to ignore during normalization:
- comment_quad_marker_<dynamic_number>_us
- All comment content and processing markers

ANALYSIS REQUIREMENTS:
=====================
1. **SYSTEMATIC NORMALIZATION**: Apply consistent normalization to both statements before comparison
2. **COMPREHENSIVE IDENTIFICATION**: Identify ALL structural and functional differences between normalized statements
3. **DETAILED EXPLANATION**: Provide specific details about each difference found and its impact on functionality
4. **PRECISE CATEGORIZATION**: Distinguish between ignorable formatting differences and critical functional differences
5. **COMPLETE COVERAGE**: Ensure no structural differences are missed in the analysis
6. **TRANSFORMATION GUIDANCE**: If statements don't match, provide specific actionable guidance for enhancement:
   - Identify exact patterns that need to change
   - Specify target patterns they should become
   - Provide implementation methods for the transformation
   - Include variations that should be handled
   - Give step-by-step implementation guidance
   - Explain parameter extraction and reconstruction
"""

    return prompt
