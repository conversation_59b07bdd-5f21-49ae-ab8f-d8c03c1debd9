import uuid
from fastapi import <PERSON><PERSON>out<PERSON>, HTTPException, Form

# Import configuration and LLM components
from llm_config.config_manager import ConfigManager
from common.common import create_llm

# Import Stage 2 workflow components
from Conversion_Agent_Stage2.workflow.graph_builder import Stage2GraphBuilder

router = APIRouter()


class Stage2ConversionData:
    """Data class for Stage 2 conversion request parameters."""
    def __init__(self, process_type, schema_name, objecttype, object_name, migration_name, cloud_category,
                 source_statement=None, converted_statement=None):
        self.process_type = process_type
        self.schema_name = schema_name
        self.objecttype = objecttype
        self.object_name = object_name
        self.migration_name = migration_name
        self.cloud_category = cloud_category
        # QBook-specific parameters
        self.source_statement = source_statement
        self.converted_statement = converted_statement


def run_stage2_conversion_workflow(request_data: Stage2ConversionData):
    """Execute Stage 2 conversion workflow to update QMigrator modules."""
    try:
        print(f"🚀 Starting Stage 2 conversion workflow...")

        # TODO: Pre-setup migration environment when needed
        print("🔧 Stage 2 environment setup - TODO: Implement when required")

        # TODO: Add database credential validation when needed
        print("🔐 Database credentials setup - TODO: Implement when required")

        # Initialize LLM configuration
        config_manager = ConfigManager()
        llm_provider = config_manager.get_llm_provider()
        print(f"🔧 Initializing {llm_provider} LLM for Stage 2...")
        llm = create_llm(llm_provider, config_manager)
        print(f"✅ Successfully initialized {llm_provider} for Stage 2 processing")

        # Initialize the Stage 2 workflow graph builder with the LLM
        graph_builder = Stage2GraphBuilder(llm)
        graph_builder.setup_graph()

        # Generate workflow visualization for debugging and documentation
        graph_builder.save_graph_image(graph_builder.graph)

        # Generate unique thread ID for this Stage 2 workflow
        thread_id = str(uuid.uuid4())
        print(f"🧵 Stage 2 workflow thread ID: {thread_id}")

        # Execute the Stage 2 workflow with initial state
        print("🔄 Starting Stage 2 workflow execution...")

        # Prepare workflow state based on process type
        workflow_state = {
            "migration_name": request_data.migration_name,
            "process_type": request_data.process_type,
            "schema_name": request_data.schema_name,
            "object_name": request_data.object_name,
            "cloud_category": request_data.cloud_category,
            "current_statement_index": 0,
            "current_attempt": 1,
            "max_attempts": 3,
            "features_validation_passed": False,
            "responsible_features_valid": False,

            "ai_statements_match": False,
            "workflow_completed": False,
            "attempt_history": []
        }

        # Add process type specific parameters
        if request_data.process_type == "qmigrator":
            workflow_state["objecttype"] = request_data.objecttype
        elif request_data.process_type == "qbook":
            workflow_state["source_statement"] = request_data.source_statement
            workflow_state["converted_statement"] = request_data.converted_statement

        result = graph_builder.invoke_graph(workflow_state, thread_id=thread_id)

        print("✅ Stage 2 workflow completed successfully")
        # print(f"📊 Final result: {result}")

        return result

    except Exception as e:
        print(f"❌ Stage 2 workflow failed: {str(e)}")
        raise


@router.post("/conversion-agent-stage2")
def conversion_agent_stage2(
    process_type: str = Form(..., description="Process type: 'qmigrator' for object-level or 'qbook' for statement-level"),
    migration_name: str = Form(..., description="Name of the migration"),
    schema_name: str = Form(..., description="Schema name for the database object"),
    object_name: str = Form(..., description="Name of the object being processed"),
    cloud_category: str = Form(..., description="Cloud category: 'local' or 'cloud'"),
    # QMigrator-specific parameters (optional, required only for qmigrator process type)
    objecttype: str = Form(None, description="Type of the object being processed (required for qmigrator process type)"),
    # QBook-specific parameters (optional, required only for qbook process type)
    source_statement: str = Form(None, description="Individual source statement (required for qbook process type)"),
    converted_statement: str = Form(None, description="Individual converted statement (required for qbook process type)")
):
    """
    Stage 2 Conversion Agent - QMigrator Module Updates.

    Processes AI corrections from Stage 1 to update QMigrator Python modules
    through a workflow with retry mechanism.

    Common Parameters:
    - process_type: 'qmigrator' for object-level or 'qbook' for statement-level processing
    - migration_name: Name of the migration (e.g., Oracle_Postgres14)
    - schema_name: Schema name for the database object
    - object_name: Name of the object being processed

    QMigrator Process Type - Additional Required:
    - objecttype: Type of the object (e.g., procedure, function, view)

    QBook Process Type - Additional Required:
    - source_statement: Single source statement for processing
    - converted_statement: Single converted statement for processing

    Note: QBook processes ONE statement at a time. QMigrator reads files during execution.
    """

    try:
        print(f"🚀 Starting Stage 2 conversion workflow...")
        print(f"🔀 Process type: {process_type}")

        # Validate process type specific mandatory parameters
        if process_type == "qmigrator":
            # QMigrator mandatory parameters: schema_name, object_name, objecttype, migration_name
            if not objecttype:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "Missing required parameters for qmigrator process type",
                        "message": "objecttype is required for qmigrator processing",
                        "required_parameters": ["schema_name", "object_name", "objecttype", "migration_name"]
                    }
                )
            print(f"📁 QMigrator processing - Object type: {objecttype}")

        elif process_type == "qbook":
            # QBook mandatory parameters: schema_name, object_name, source_statement, converted_statement, migration_name
            if not source_statement or not converted_statement:
                raise HTTPException(
                    status_code=400,
                    detail={
                        "error": "Missing required parameters for qbook process type",
                        "message": "source_statement and converted_statement are required for qbook processing",
                        "required_parameters": ["schema_name", "object_name", "source_statement", "converted_statement", "migration_name"]
                    }
                )
            print(f"📝 QBook single statement processing - source_statement length: {len(source_statement)}")
            print(f"📝 QBook single statement processing - converted_statement length: {len(converted_statement)}")
            print("📋 QBook mode: Processing ONE statement only")
        else:
            raise HTTPException(
                status_code=400,
                detail={
                    "error": "Invalid process type",
                    "message": f"process_type must be 'qmigrator' or 'qbook', got: {process_type}",
                    "valid_process_types": ["qmigrator", "qbook"]
                }
            )

        # Create Stage 2 request object from form data
        request_data = Stage2ConversionData(
            process_type=process_type,
            schema_name=schema_name,
            objecttype=objecttype,
            object_name=object_name,
            migration_name=migration_name,
            cloud_category=cloud_category,
            source_statement=source_statement,
            converted_statement=converted_statement
        )

        # Execute the Stage 2 conversion workflow synchronously
        result = run_stage2_conversion_workflow(request_data)

        # Return successful completion with results
        response = {
            "message": "Stage 2 Conversion Agent Process Completed Successfully",
            "status": "completed",
            "stage": "Stage 2 - QMigrator Module Updates",
            "process_type": process_type,
            "schema_name": schema_name,
            "migration_name": migration_name,
            "object_name": object_name,
            "objecttype": objecttype,
            "result": result
        }

        # Add qbook-specific parameters to response if applicable
        if process_type == "qbook":
            response["source_statement_length"] = len(source_statement) if source_statement else 0
            response["converted_statement_length"] = len(converted_statement) if converted_statement else 0

        return response

    except Exception as e:
        print(f"❌ Failed to start Stage 2 conversion workflow: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail={
                "error": "Failed to start Stage 2 conversion workflow",
                "message": str(e),
                "stage": "Stage 2 - QMigrator Module Updates"
            }
        )