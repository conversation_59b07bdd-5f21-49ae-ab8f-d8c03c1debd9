"""
Prompts for sequential driver module enhancement in Stage 2 conversion analysis.
"""
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms

def create_sequential_enhancement_prompt(
    combined_code: str,
    enhancement_context: dict
) -> str:
    """
    Create sequential enhancement prompt with responsibility reasoning and sequential context.
    
    Args:
        combined_code: Combined responsible modules code
        enhancement_context: Sequential enhancement context with responsibility reasoning
    
    Returns:
        Formatted prompt string for sequential module enhancement
    """
    
    db_terms = enhancement_context.get('db_terms', {})
    source_db = db_terms.get('source_db', 'Source Database')
    target_db = db_terms.get('target_db', 'Target Database')
    
    responsibility_section = build_responsibility_section(enhancement_context)
    output_comparison_section = build_output_comparison_section(enhancement_context)
    attempt_history_section = build_attempt_history_section(enhancement_context)
    
    prompt = f"""
You are enhancing RESPONSIBLE MODULES in a sequential execution pipeline with specific responsibility reasoning.

CRITICAL ENHANCEMENT APPROACH - PRESERVE AND ADD:
================================================
- **PRESERVE all existing module code and functionality completely**
- **DO NOT MODIFY or remove any existing logic**
- **ADD supplementary transformation logic after existing code**
- **MAINTAIN backward compatibility for all existing scenarios**
- **JUST ADD what's needed to achieve expected output**

RESPONSIBILITY ANALYSIS CONTEXT:
===============================
{responsibility_section}

SEQUENTIAL EXECUTION CONTEXT:
============================
Original {source_db} Statement:
{enhancement_context['original_input_statement']}

Pre-Execution Completed: {', '.join(enhancement_context['pre_execution_modules'])}
↓
Current Pre-processed State:
{enhancement_context['pre_processed_input']}
↓
YOUR TASK: Transform pre-processed state to match expected AI corrected {target_db} output
↓
Post-Execution Will Run: (post-features will execute after your enhancement)
↓
Expected AI Corrected {target_db} Output:
{enhancement_context['expected_final_output']}

DEPLOYMENT ERROR CONTEXT:
========================
Error Details: {enhancement_context.get('deployment_error', 'No deployment error available')}
Error Location: Statement {enhancement_context.get('current_attempt', 1)}
This error occurred because the current transformation is incomplete or incorrect.

OUTPUT COMPARISON ANALYSIS:
==========================
{output_comparison_section}

{attempt_history_section}

RESPONSIBLE MODULES TO ENHANCE:
==============================
{combined_code}

ENHANCEMENT GOAL:
================
Based on the responsibility analysis and deployment error context, enhance the responsible modules to transform:

FROM: Current pre-processed state (after pre-execution modules)
TO: Expected AI corrected {target_db} output (that will resolve the deployment error)

TRANSFORMATION STRATEGY:
=======================
1. ANALYZE the gap between current pre-processed state and expected output
2. IDENTIFY what specific transformations are missing to resolve the deployment error
3. USE the responsibility analysis as your solution guide
4. IMPLEMENT flexible patterns that handle multiple variations:
   - Whitespace variations (spaces, tabs, newlines between keywords)
   - Case variations (upper, lower, mixed case)
   - Bracket spacing variations (tight vs spaced brackets)
   - Parameter ordering differences
   - Nested structure variations

RESPONSIBILITY-DRIVEN SOLUTION:
==============================
Based on the responsibility analysis provided above:
- Focus on the specific transformation patterns identified
- Implement the solution approaches suggested
- Handle all the variations mentioned in the guidance
- Use the example patterns as implementation templates

DEPLOYMENT ERROR CONTEXT:
=========================
Error: {enhancement_context['deployment_error']}
Original {source_db}:
{enhancement_context['original_input_statement']}
Original {target_db}:
{enhancement_context['original_failed_output']}

FEEDBACK CONTEXT:
================
{enhancement_context.get('ai_comparison_feedback', '')}
{enhancement_context.get('iteration_feedback', '')}

MIXED CONVERSION STATE CONTEXT:
==============================
The input may contain MIXED conversion states:
- Some SQL constructs already converted to {target_db}
- Some SQL constructs still in {source_db} format
- Your modules must handle BOTH states and transform to expected output
- Focus on transforming current input → expected output

INTERMEDIATE CONTENT AWARENESS:
==============================
**CRITICAL**: Handle intermediate content between SQL elements:
- **Comments**: comment_quad_marker_X_us placeholders
- **Markers**: Various comment and processing markers
- **Whitespace**: Tabs, spaces, newlines between SQL elements
- **PRESERVE**: All intermediate content during transformations
- **FLEXIBLE PATTERNS**: Use .*? to match ANY content between SQL elements

SQL CLAUSE INTEGRITY PRESERVATION:
=================================
**CRITICAL**: Maintain complete SQL clause structure during transformations:

**COMPLETE CLAUSE UNITS**:
- **ORDER BY clause**: `ORDER BY column_name [ASC|DESC]` - treat as single unit
- **LIMIT clause**: `LIMIT number` - treat as single unit
- **WHERE clause**: `WHERE conditions` - treat as single unit
- **GROUP BY clause**: `GROUP BY columns` - treat as single unit

**CLAUSE REORDERING PRINCIPLES**:
- **CAPTURE COMPLETE CLAUSES**: Include all parts (keywords + expressions + modifiers)
- **PRESERVE CLAUSE INTEGRITY**: Never split clause components across capture groups
- **MAINTAIN SQL SYNTAX**: Ensure reordered clauses follow valid SQL syntax rules
- **VALIDATE STRUCTURE**: Verify output produces valid SQL statements

**GENERIC CLAUSE PATTERN EXAMPLES**:
```python
# CORRECT: Capture complete ORDER BY clause
pattern = r'(LIMIT\s+\d+)(.*?)(ORDER\s+BY\s+[\w.]+(?:\s+(?:ASC|DESC))?)'
replacement = r'\3\2 \1'  # ORDER BY + intermediate + LIMIT

# CORRECT: Handle multiple clause components
pattern = r'(WHERE\s+[^ORDER]+?)(.*?)(ORDER\s+BY\s+[\w.]+(?:\s+(?:ASC|DESC))?)(.*?)(LIMIT\s+\d+)'
replacement = r'\1\2\3\4\5'  # Maintain proper SQL order

# WRONG: Splitting clause components
pattern = r'(ORDER)(.*?)(BY\s+[\w.]+)'  # ❌ Breaks ORDER BY clause
```

**SQL SYNTAX VALIDATION**:
- **KEYWORD COMPLETENESS**: Ensure SQL keywords remain with their expressions
- **EXPRESSION INTEGRITY**: Keep column names, functions, and modifiers together
- **CLAUSE BOUNDARIES**: Identify where one clause ends and another begins
- **SYNTAX RULES**: Follow database-specific SQL syntax requirements

FLEXIBLE PATTERN CONSTRUCTION:
=============================
- **DYNAMIC ANALYSIS**: Compare current vs expected output to identify transformation needs
- **COMPONENT MAPPING**: Identify which elements need to be captured and repositioned
- **FLEXIBLE CAPTURE**: Use (.*?) wildcards to handle any content between elements
- **PRESERVE EVERYTHING**: Capture all intermediate content and preserve in transformation
- **GENERIC APPROACH**: Create patterns based on actual structural differences, not assumptions
- **ADAPTIVE MATCHING**: Handle varying whitespace, formatting, and intermediate content
- **COMPLETE RECONSTRUCTION**: Ensure all captured components are properly reassembled
- **COMMENT MARKERS**: Specifically handle comment_quad_marker_X_us placeholders between SQL clauses

STRUCTURAL ANALYSIS FOCUS:
=========================
- **EXAMINE**: Clause ordering and element positioning issues
- **IDENTIFY**: Structural differences between current and expected output
- **ADDRESS**: Element positioning, clause ordering, syntax arrangement
- **UNDERSTAND**: Deployment error context for structural problems
- **REPOSITION**: Elements rather than removing or adding elements
- **PRESERVE**: Complete clause integrity during transformation

GENERIC PATTERN APPROACH:
========================
- **CREATE**: Patterns that work regardless of intermediate content
- **DESIGN**: Robust patterns that match real-world SQL formatting variations
- **IMPLEMENT**: Context-independent transformations
- **ENSURE**: Patterns work in any SQL statement type or context
- **VALIDATE**: Transformations preserve surrounding SQL code correctly

ADDITIVE ENHANCEMENT PRINCIPLES:
===============================
- **PRESERVE**: All existing regex patterns and logic
- **KEEP**: All existing code unchanged
- **ADD**: New logic after existing code
- **ADD supplementary logic after existing code**: Build on existing functionality
- **APPLY**: New transformations only if existing logic doesn't transform
- **MAINTAIN**: Backward compatibility for all scenarios
- **COPY**: All existing code exactly as written
- **EXTEND**: Functionality without breaking existing patterns
- **DO NOT MODIFY existing functionality**: Only add supplementary logic

🚨 CRITICAL BOUNDARY MARKER PRESERVATION 🚨:
============================================
**⚠️ SYSTEM WILL FAIL IF BOUNDARY MARKERS ARE MISSING ⚠️**

**MANDATORY STRUCTURE - COPY EXACTLY**:
```
# === MODULE: [any_module_name] START ===
[your enhanced module code here]
# === MODULE: [any_module_name] END ===
```

**🚨 BOUNDARY MARKER RULES - NO EXCEPTIONS 🚨**:
- **FIRST LINE**: Must be exactly `# === MODULE: [module_name] START ===`
- **LAST LINE**: Must be exactly `# === MODULE: [module_name] END ===`
- **PRESERVE EXACTLY**: Keep all boundary markers exactly as they appear in input
- **MODIFY ONLY CONTENT**: Only change the functional code between markers
- **MAINTAIN FORMAT**: Do not alter marker spacing, capitalization, or punctuation
- **COMPLETE STRUCTURE**: Always include both START and END markers for each module
- **⚠️ MISSING MARKERS = SYSTEM FAILURE ⚠️**

**🚨 CRITICAL ERROR PREVENTION 🚨**:
====================================
**⚠️ BOUNDARY MARKERS ARE MANDATORY - NEVER REMOVE THEM ⚠️**

**STEP 1**: ALWAYS start your enhanced_code with the EXACT boundary markers from input:
```
# === MODULE: xml_sequence START ===
```

**STEP 2**: ALWAYS end your enhanced_code with the EXACT boundary markers from input:
```
# === MODULE: xml_sequence END ===
```

**⚠️ FAILURE TO PRESERVE BOUNDARY MARKERS WILL CAUSE SYSTEM FAILURE ⚠️**

**MANDATORY EXCEPTION HANDLING**: Use ONLY these safe patterns:
```python
try:
    # your regex code
except:
    pass  # NO VARIABLES - NO SCOPE ISSUES
```
OR
```python
try:
    # your regex code
except re.error:
    pass  # NO VARIABLES - NO SCOPE ISSUES
```

**NEVER USE**: `except re.error as e:` unless you handle `e` completely within the except block.

ENHANCEMENT RULES:
==================
- **PRESERVE**: All existing module code and functionality completely
- **ADD**: Supplementary logic after existing code to bridge gaps
- **CRITICAL**: Only modify code within responsible module boundary markers
- **MANDATORY**: Preserve all boundary markers exactly: # === MODULE: ... START/END ===
- **ERROR-PROOF**: Use only the safe exception handling patterns shown above
- Focus on the specific responsibilities identified for each module
- Analyze actual input/output differences to create appropriate transformations
- Transform actual responsible input to achieve expected output
- Consider that post-execution modules will run after your enhancement
- Create patterns based on actual data differences
- **BUILD**: On existing patterns, don't replace them

ITERATIVE ENHANCEMENT GUIDANCE:
==============================
- **ANALYZE**: What existing patterns and logic are already present
- **UNDERSTAND**: How current transformations work
- **IDENTIFY**: What gaps exist in current pattern coverage
- **EXTEND existing patterns**: To handle missing cases without replacing them
- **ADD supplementary patterns**: That work with existing logic
- **TEST**: Enhanced patterns work with current and new scenarios
- **BUILD**: On existing functionality instead of replacing it

GENERIC PATTERN CONSTRUCTION PRINCIPLES:
=======================================
**ELEMENT IDENTIFICATION STRATEGY**:
- **ANALYZE**: Current vs expected output to identify which elements need reordering
- **IDENTIFY**: All distinct components that need to be captured and repositioned
- **SEPARATE**: Fixed elements (keywords) from variable elements (expressions, values)
- **MAP**: Each component's current position vs expected position

**FLEXIBLE CAPTURE METHODOLOGY**:
- **CAPTURE**: Each distinct element as a separate group: (element1)(intermediate)(element2)(expressions)
- **PRESERVE**: All intermediate content between elements using (.*?) wildcards
- **EXTRACT**: Variable expressions and values that belong to specific elements
- **MAINTAIN**: Complete element integrity during repositioning

**GENERIC RECONSTRUCTION APPROACH**:
- **RECONSTRUCT**: Elements in expected order using captured groups
- **COMBINE**: Fixed keywords with their associated variable expressions
- **PRESERVE**: All intermediate content in appropriate positions
- **ENSURE**: Complete clause structure and syntax correctness

**COMMENT MARKER HANDLING (SPECIFIC SYSTEM REQUIREMENT)**:
=========================================================
- **RECOGNIZE**: comment_quad_marker_X_us patterns (where X is a digit)
- **PRESERVE**: These markers exactly as they appear in the input
- **POSITION**: Maintain markers in appropriate locations during reordering
- **EXAMPLE**: When reordering clauses, preserve comment markers between elements:
  ```
  Input:  element1 comment_quad_marker_0_us element2
  Output: element2 comment_quad_marker_0_us element1 (if reordering needed)
  ```
- **PATTERN**: Use (.*?) to capture any content including these markers
- **MAINTAIN**: Exact marker format and positioning during transformations

**GENERIC CLAUSE REORDERING WITH INTERMEDIATE CONTENT**:
=======================================================
When SQL clauses need reordering but have intermediate content between them:

**EXAMPLE PATTERN**:
```python
# Generic pattern for reordering clause1 and clause2 with any content between
pattern = r'(clause1_pattern)(.*?)(clause2_pattern)'
replacement = r'\3\2\1'  # Reorder: clause2 + intermediate_content + clause1

# Real example with flexible matching:
pattern = r'(\w+\s+\d+)(.*?)(\w+\s+by\s+\w+)'
replacement = r'\3\2 \1'
```

**INTERMEDIATE CONTENT EXAMPLES**:
- Comments: `comment_quad_marker_0_us`
- Newlines and whitespace: `\n\s*`
- Mixed content: `comment_marker\nWHERE condition\n`
- Multiple markers: `marker1\ncode\nmarker2`

**FLEXIBLE CAPTURE APPROACH**:
```python
# Use (.*?) to capture ANY content between SQL elements
data = re.sub(r'(first_element)(.*?)(second_element)', r'\3\2\1', data, flags=re.DOTALL|re.I)
```

UNIVERSAL TRANSFORMATION METHODOLOGY:
===================================
**SYSTEMATIC PATTERN CONSTRUCTION PROCESS**:
1. **ANALYZE INPUT STRUCTURE**: Examine the actual input character by character
   - Identify all SQL keywords (any keywords in your context)
   - Locate all intermediate content (comments, markers, whitespace)
   - Map the current sequence of elements

2. **ANALYZE EXPECTED OUTPUT**: Examine the target output structure
   - Identify the desired sequence of elements
   - Note which elements need to be repositioned
   - Understand what content must be preserved

3. **DESIGN FLEXIBLE CAPTURE PATTERN**:
   - Use separate capture groups for each distinct element
   - Use (.*?) for ALL intermediate content between elements
   - Account for case variations (use re.I flag)
   - Handle multi-line structures (use re.DOTALL flag)

4. **CONSTRUCT RECONSTRUCTION LOGIC**:
   - Reassemble captured groups in the desired order
   - Preserve all intermediate content in appropriate positions
   - Ensure complete clause structure integrity

**SYSTEMATIC PATTERN TESTING APPROACH**:
- **STEP 1**: Test pattern against actual input structure
- **STEP 2**: Verify all elements are captured correctly
- **STEP 3**: Confirm intermediate content is preserved
- **STEP 4**: Validate output matches expected structure exactly

**SYSTEMATIC ENHANCEMENT ARCHITECTURE**:
```python
def enhanced_function(data, schema):
    # PRESERVE: All existing logic exactly as-is
    original_data = data
    # ... existing code unchanged ...

    # SYSTEMATIC TRANSFORMATION APPROACH:
    if data == original_data:  # Existing logic didn't transform
        # STEP 1: ANALYZE actual input structure
        # Look at the actual data character by character
        # Identify SQL keywords, intermediate content, expressions

        # STEP 2: DESIGN flexible capture pattern with validation
        flexible_pattern = r'(element1_pattern)(.*?)(element2_pattern)(.*?)(expression_pattern)'

        # STEP 3: VALIDATE pattern before use
        try:
            compiled_pattern = re.compile(flexible_pattern, re.DOTALL|re.I)
            if re.search(flexible_pattern, data, re.DOTALL|re.I):
                # STEP 4: APPLY transformation with validated pattern
                data = re.sub(flexible_pattern, r'reordered_groups', data, flags=re.DOTALL|re.I)
        except re.error as e:
            # Handle regex compilation errors gracefully
            # Complete all error handling within this except block
            # DO NOT reference 'e' outside this block
            return original_data  # Return original data on regex error

    return data
```

**ERROR HANDLING FOR REGEX PATTERNS**:
=====================================
```python
# SAFE PATTERN APPLICATION:
def apply_safe_regex_transformation(data, pattern, replacement, flags=re.DOTALL|re.I):
    try:
        # Validate pattern compilation
        compiled_pattern = re.compile(pattern, flags)

        # Test if pattern matches
        if re.search(pattern, data, flags):
            # Apply transformation
            return re.sub(pattern, replacement, data, flags=flags)
        else:
            # Pattern doesn't match, return original data
            return data
    except re.error:
        # Handle error completely within except block
        # Use simple except without variables to avoid scope issues
        return data

    # Return original data if no transformation applied
    return data
```

**SAFE REGEX ENHANCEMENT PATTERN**:
==================================
```python
def enhanced_module(data, schema):
    # Preserve existing logic
    original_data = data
    # ... existing code unchanged ...

    # Add supplementary enhancement with safe error handling
    if data == original_data:  # Only if existing logic didn't transform
        pattern = r'(your_flexible_pattern_here)'
        replacement = r'your_replacement_here'

        try:
            if re.search(pattern, data, re.DOTALL|re.I):
                data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
        except re.error as e:
            # Complete error handling within except block
            # Return original data on any regex error
            return original_data

    return data
```

**PYTHON EXCEPTION HANDLING SYNTAX**:
====================================
✅ **CORRECT Exception Handling**:
```python
try:
    # Your regex code here
    data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
except re.error:
    # Handle the error without variables to avoid scope issues
    return data  # Return original data on error
```

❌ **WRONG Exception Handling**:
```python
try:
    data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
except re.error as undefined_var:
    # ERROR: Using made-up variable names causes issues
    pass
```

❌ **WRONG Variable Scope**:
```python
try:
    data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
except re.error as scope_var:
    pass  # Exception handled but variable goes out of scope

# ERROR: variable not defined outside the except block
# Referencing scope_var here would cause NameError
```

**CRITICAL PYTHON SYNTAX RULES**:
- **SCOPE**: Exception variables only exist within the except block
- **NEVER reference exception variables outside try/except blocks**
- **ALWAYS handle errors completely within the except block**
- **STANDARD PRACTICE**: Use `as e` for exception variables
- **AVOID**: Made-up variable names like `regex_error`, `pattern_error`
- **USE**: Simple, standard names like `e`, `err`, `error`
- **COMPLETE HANDLING**: Do all error processing within the except block

**FOOLPROOF EXCEPTION HANDLING TEMPLATE**:
=========================================
**ALWAYS USE THIS EXACT PATTERN**:
```python
# SAFE PATTERN - Copy this exactly:
try:
    if re.search(pattern, data, re.DOTALL|re.I):
        data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
except:
    # Simple except without variable - no scope issues
    pass  # Continue with original data

# Alternative with variable (use ONLY within except block):
try:
    if re.search(pattern, data, re.DOTALL|re.I):
        data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
except re.error:
    # No variable reference - no scope issues
    pass  # Continue with original data
```

**NEVER DO THIS** (causes NameError):
```python
try:
    data = re.sub(pattern, replacement, data)
except re.error as e:
    error_msg = str(e)  # OK - within except block
# print(error_msg)  # ERROR if error_msg used outside
# print(e)  # ERROR - 'e' not defined outside except
```

**PATTERN CONSTRUCTION VALIDATION CHECKLIST**:
- ✅ Pattern uses (.*?) for intermediate content
- ✅ Pattern includes re.DOTALL|re.I flags
- ✅ Pattern captures complete expressions, not just keywords
- ✅ Pattern tested against actual input structure
- ✅ Reconstruction preserves all captured content
- ✅ Output matches expected structure exactly

**SYSTEMATIC PATTERN CONSTRUCTION EXAMPLES**:
===========================================
**STEP-BY-STEP PATTERN BUILDING**:

**STEP 1 - ANALYZE ACTUAL INPUT STRUCTURE**:
```
Generic Input: "element1_content\nintermediate_content\nelement2_content"
Elements Found:
- Element 1: "element1_content" (first component)
- Intermediate: "\nintermediate_content\n" (newlines + markers/comments)
- Element 2: "element2_content" (second component)
- Expression: "variable_expression_content" (dynamic content)
```

**STEP 2 - DESIGN FLEXIBLE CAPTURE PATTERN**:
```python
# Capture each component separately with flexible intermediate content
pattern = r'(element1_pattern)(.*?)(element2_pattern)'
# Group 1: first element pattern
# Group 2: ANY intermediate content (including markers, newlines)
# Group 3: second element pattern (with expressions)
```

**STEP 3 - CONSTRUCT RECONSTRUCTION**:
```python
# Reorder: element2 first, then intermediate content, then element1
replacement = r'\3\2\1'
# Result: "element2_content\nintermediate_content\nelement1_content"
```

**CRITICAL PATTERN REQUIREMENTS**:
- **ALWAYS use re.DOTALL|re.I flags** for case and newline handling
- **ALWAYS use (.*?) for intermediate content** - never assume specific content
- **ALWAYS capture complete expressions** - don't stop at first word
- **ALWAYS test pattern against actual input structure** - not assumptions

**REGEX VALIDATION AND ERROR PREVENTION**:
=======================================
**MANDATORY REGEX VALIDATION**:
- **TEST PATTERNS**: Validate regex patterns before applying them
- **GROUP REFERENCES**: Ensure replacement strings match capture groups in patterns
- **ESCAPE SPECIAL CHARS**: Properly escape parentheses, brackets, and special characters
- **AVOID RUNTIME ERRORS**: Never create patterns that cause "invalid group reference" errors

**COMMON REGEX ERRORS TO AVOID**:
❌ **INVALID GROUP REFERENCE**: Using \\1, \\2 without corresponding capture groups
❌ **UNESCAPED PARENTHESES**: Parentheses in replacement strings without proper escaping
❌ **MISMATCHED GROUPS**: More group references than capture groups in pattern
❌ **COMPILATION ERRORS**: Malformed regex patterns that fail at runtime
❌ **VARIABLE SCOPE ERRORS**: Referencing exception variables outside except blocks

**REGEX PATTERN VALIDATION CHECKLIST**:
- ✅ Pattern compiles without errors: `re.compile(pattern)`
- ✅ Replacement string matches number of capture groups
- ✅ Special characters properly escaped in replacement
- ✅ Pattern tested against actual input data
- ✅ No "invalid group reference" errors during execution
- ✅ Exception variables only used within except blocks
- ✅ All error handling completed within try/except structure

**🚨 GENERIC BOUNDARY MARKER PRESERVATION EXAMPLE 🚨**:
========================================================
**INPUT COMBINED MODULE**:
```
# === MODULE: [any_module_name] START ===
import re
def module_function(data, schema):
    # existing logic here
    return data
# === MODULE: [any_module_name] END ===
```

**✅ CORRECT OUTPUT** (with enhancement and preserved markers):
```
# === MODULE: [any_module_name] START ===
import re
def module_function(data, schema):
    # existing logic here

    # ENHANCED: Add supplementary transformation with safe error handling
    try:
        if re.search(r'your_pattern_here', data, re.DOTALL|re.I):
            data = re.sub(r'your_pattern_here', r'your_replacement_here', data, flags=re.DOTALL|re.I)
    except:
        pass

    return data
# === MODULE: [any_module_name] END ===
```

**❌ WRONG OUTPUT** (boundary markers removed - CAUSES SYSTEM FAILURE):
```
import re
def module_function(data, schema):
    # This will cause "Could not extract module with markers" error!
    return data
```

**GENERIC REGEX ERROR PREVENTION EXAMPLES**:
============================================
❌ **WRONG** (Invalid group reference):
```python
# Pattern has no capture groups, but replacement tries to use \1
data = re.sub(r'PATTERN_WITHOUT_GROUPS', r'replacement_with_\1', data)
# ERROR: invalid group reference 1 at position 14
```

✅ **CORRECT** (Proper capture groups):
```python
# Pattern has capture group, replacement uses it correctly
data = re.sub(r'PATTERN_WITH_(.*?)_GROUPS', r'replacement_with_\1', data, flags=re.DOTALL|re.I)
```

✅ **CORRECT** (No group references needed):
```python
# Simple replacement without group references
data = re.sub(r'PATTERN_TO_MATCH', 'replacement_text', data, flags=re.DOTALL|re.I)
```

**GENERIC ERROR-PROOF ENHANCEMENT PATTERN**:
============================================
**USE THIS EXACT TEMPLATE - NO EXCEPTIONS**:
```python
def any_module_function(data, schema):
    # STEP 1: Preserve all existing code exactly
    original_data = data
    # ... existing module code unchanged ...

    # STEP 2: Add safe supplementary enhancement
    if data == original_data:  # Only if existing logic didn't transform
        # Define your generic pattern and replacement
        pattern = r'(generic_pattern_here)'
        replacement = r'generic_replacement_here'

        # STEP 3: Apply with bulletproof error handling
        try:
            if re.search(pattern, data, re.DOTALL|re.I):
                data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
        except:
            # Simple except - no variables, no scope issues
            pass  # Continue with original data on any error

    return data
```

**ALTERNATIVE SAFE PATTERN** (if you need error handling):
```python
def any_module_function(data, schema):
    original_data = data
    # ... existing code ...

    if data == original_data:
        pattern = r'(generic_pattern_here)'
        replacement = r'generic_replacement_here'

        try:
            if re.search(pattern, data, re.DOTALL|re.I):
                data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
        except re.error:
            # No variable - no scope issues
            data = original_data  # Reset to original on error

    return data
```

**WRONG OUTPUT** (boundary markers removed):
```
import re
def join(data, sch):
    # This will cause extraction failure!
    return data
```

ELEMENT RECONSTRUCTION PRINCIPLES:
=================================
**COMPONENT SEPARATION STRATEGY**:
- **KEYWORDS**: Fixed SQL keywords that define clause types
- **EXPRESSIONS**: Variable content that belongs to specific clauses
- **SEPARATORS**: Whitespace, punctuation, and structural elements
- **INTERMEDIATE**: Comments, markers, and other preservable content

**INTELLIGENT RECONSTRUCTION APPROACH**:
- **ASSOCIATE**: Variable expressions with their correct parent keywords
- **REORDER**: Complete clause structures (keyword + expressions) as units
- **MAINTAIN**: Proper SQL syntax and clause relationships
- **PRESERVE**: All content while ensuring correct structural arrangement

**GENERIC PATTERN CONSTRUCTION MISTAKES TO AVOID**:
===================================================
❌ **MISTAKE 1**: Assuming only whitespace between elements
```python
# WRONG: r'(ELEMENT1\s+ELEMENT2)'
# FAILS: Doesn't handle intermediate content like comment markers
```

❌ **MISTAKE 2**: Not using case-insensitive matching
```python
# WRONG: r'(ELEMENT1)(.*?)(ELEMENT2)' without re.I
# FAILS: Doesn't match lowercase variations
```

❌ **MISTAKE 3**: Not capturing complete expressions
```python
# WRONG: r'(element1)(.*?)(ELEMENT2)'
# FAILS: Doesn't capture the complete element2 expression
```

❌ **MISTAKE 4**: Not using DOTALL flag for multiline content
```python
# WRONG: re.sub(pattern, replacement, data, re.I)
# FAILS: Doesn't handle newlines in intermediate content
```

✅ **CORRECT GENERIC SYSTEMATIC APPROACH**:
```python
# STEP 1: Analyze actual structure
# Input: "element1_content\nintermediate_content\nelement2_content"

# STEP 2: Design flexible pattern
pattern = r'(element1_pattern)(.*?)(element2_pattern)'

# STEP 3: Apply with proper flags
data = re.sub(pattern, r'\3\2\1', data, flags=re.DOTALL|re.I)

# RESULT: "element2_content\nintermediate_content\nelement1_content"
```

**TRANSFORMATION VALIDATION PRINCIPLES**:
- **VERIFY**: Output matches expected structure exactly
- **ENSURE**: All original content is preserved in correct positions
- **CONFIRM**: SQL syntax is valid and complete
- **VALIDATE**: Transformation works for similar structural patterns
- **TEST**: Pattern against actual input, not assumptions

COMPREHENSIVE VALIDATION CHECKLIST:
==================================
Before submitting your response, verify:

**BOUNDARY MARKERS**:
✅ All boundary markers are preserved exactly: # === MODULE: ... START/END ===
✅ No boundary markers were removed or modified
✅ Each module has both START and END markers
✅ Only functional code between markers was modified
✅ Enhanced code maintains complete module structure

**REGEX PATTERNS**:
✅ All regex patterns compile without errors
✅ Group references (\\1, \\2) match capture groups in patterns
✅ Special characters properly escaped in replacement strings
✅ Patterns tested against actual input structure
✅ No "invalid group reference" or compilation errors
✅ Exception handling uses correct Python syntax (except re.error as e)

**TRANSFORMATION LOGIC**:
✅ Enhanced patterns handle intermediate content with (.*?)
✅ Patterns use re.DOTALL|re.I flags for robustness
✅ Transformations preserve all original content
✅ Logic builds on existing functionality without breaking it

OUTPUT FORMAT (JSON):
====================
{{
  "enhanced_code": "🚨 MUST START WITH: # === MODULE: [module_name] START === AND END WITH: # === MODULE: [module_name] END === 🚨 Complete enhanced combined module with ALL existing code preserved, supplementary logic added, and ALL boundary markers preserved exactly as they were in the input",
  "analysis": "Explanation of: 1) 🚨 CONFIRMATION that boundary markers # === MODULE: [module_name] START/END === were preserved exactly 🚨, 2) What existing functionality was preserved unchanged, 3) What supplementary logic was added after existing code, 4) How components were identified and reconstructed, 5) How this achieves expected output structure without breaking existing functionality",
  "code_changed": true_if_supplementary_logic_added_false_if_no_changes_made
}}
"""
    
    return prompt

def build_responsibility_section(enhancement_context: dict) -> str:
    """Build responsibility reasoning section"""
    
    section = "RESPONSIBILITY REASONING (Why each module is responsible):\n"
    section += "=" * 60 + "\n"
    
    for module_info in enhancement_context['responsible_modules_with_reasoning']:
        section += f"\n🎯 {module_info['module_name'].upper()} MODULE:\n"
        section += f"   Responsibility: {module_info['responsibility_reason']}\n"
    
    section += f"\nOVERALL RESPONSIBILITY SUMMARY:\n{enhancement_context['responsibility_summary']}\n"
    
    return section

def build_output_comparison_section(enhancement_context: dict) -> str:
    """Build output comparison section for AI analysis using complete data (no truncation)"""

    section = "INPUT/OUTPUT TRANSFORMATION ANALYSIS:\n"
    section += "=" * 50 + "\n\n"

    # Show the actual transformation pipeline with COMPLETE data for AI analysis
    section += "1. ORIGINAL SOURCE STATEMENT (starting point):\n"
    original_input = enhancement_context.get('original_input_statement', '')
    section += f"{original_input}\n\n"

    section += "2. CURRENT PRE-PROCESSED STATE (after pre-execution modules):\n"
    pre_processed = enhancement_context.get('pre_processed_input', '')
    section += f"{pre_processed}\n\n"

    section += "3. EXPECTED AI CORRECTED OUTPUT (target to achieve):\n"
    ai_corrected = enhancement_context.get('ai_corrected_output', '')
    section += f"{ai_corrected}\n\n"

    section += "TRANSFORMATION TASK:\n"
    section += "Your responsible modules must transform the PRE-PROCESSED STATE (input #2) into the EXPECTED OUTPUT (target #3).\n"
    section += "Analyze the complete structural and content differences to create appropriate transformation patterns.\n\n"

    return section

def build_attempt_history_section(enhancement_context: dict) -> str:
    """Build attempt history section for AI learning from previous failures"""

    attempt_history = enhancement_context.get('attempt_history', [])

    if not attempt_history:
        return ""

    section = "PREVIOUS ATTEMPT HISTORY (LEARN FROM FAILURES):\n"
    section += "=" * 55 + "\n\n"

    for attempt in attempt_history:
        attempt_num = attempt.get('attempt_number', 'Unknown')
        modules_used = attempt.get('modules_used', [])
        ai_feedback = attempt.get('ai_feedback', 'No feedback available')
        final_output = attempt.get('final_output', '')

        section += f"ATTEMPT {attempt_num} (FAILED):\n"
        section += f"Modules Enhanced: {[mod.get('module_name', 'unknown') for mod in modules_used]}\n"
        section += f"Final Output: {final_output}\n"
        section += f"AI Feedback: {ai_feedback}\n"
        section += f"Status: {attempt.get('status', 'FAILED')}\n\n"

    section += "LEARNING GUIDANCE:\n"
    section += "- Analyze why previous attempts failed based on AI feedback\n"
    section += "- Avoid repeating the same enhancement patterns that failed\n"
    section += "- Try different transformation approaches for achieving expected output\n"
    section += "- Build on successful patterns from previous attempts if any\n\n"

    return section
