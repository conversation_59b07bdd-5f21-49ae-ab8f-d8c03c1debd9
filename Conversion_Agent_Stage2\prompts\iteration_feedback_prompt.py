"""
Iteration Feedback Prompts for Stage 2 Sequential Enhancement Pipeline.

This module provides prompts for managing feedback loops in the sequential enhancement
pipeline, including validation feedback, execution feedback, and AI comparison feedback.
"""

from typing import Dict, Any, List, Optional
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms


def create_validation_feedback_prompt(
    validation_errors: List[str],
    enhanced_modules: List[Dict[str, Any]],
    combined_driver_module: str,
    enhancement_context: Dict[str, Any]
) -> str:
    """
    Create prompt for validation feedback to enhance_driver_module node.
    
    Args:
        validation_errors: List of validation error messages
        enhanced_modules: List of enhanced module information
        combined_driver_module: Original combined driver module code
        enhancement_context: Context for enhancement
        
    Returns:
        Formatted prompt for validation feedback enhancement
    """
    
    db_terms = enhancement_context.get('db_terms', {})
    source_db = db_terms.get('source_db', 'Source Database')
    target_db = db_terms.get('target_db', 'Target Database')
    
    validation_errors_section = "\n".join([f"- {error}" for error in validation_errors])
    
    prompt = f"""
You are re-enhancing a combined driver module due to VALIDATION FAILURES in the sequential enhancement pipeline.

VALIDATION FEEDBACK CONTEXT:
============================
The previous enhancement attempt failed validation with the following errors:

{validation_errors_section}

CRITICAL VALIDATION REQUIREMENTS:
=================================
1. **SYNTAX VALIDATION**: All Python code must be syntactically correct
2. **BOUNDARY MARKER PRESERVATION**: All module boundary markers must be preserved exactly
3. **FUNCTIONAL CHANGE DETECTION**: Enhanced code must show actual functional improvements
4. **IMPORT STATEMENTS**: All required imports must be included
5. **EXCEPTION HANDLING**: Use only safe exception handling patterns

VALIDATION FAILURE ANALYSIS:
===========================
The enhanced modules failed validation. You must address these specific issues:

ENHANCED MODULES THAT FAILED:
============================
"""
    
    for module_info in enhanced_modules:
        prompt += f"""
MODULE: {module_info['feature_name']}
ENHANCED CODE:
{module_info['enhanced_code'][:500]}...

"""
    
    prompt += f"""
ORIGINAL COMBINED DRIVER MODULE:
===============================
{combined_driver_module}

VALIDATION FEEDBACK ENHANCEMENT TASK:
====================================
Re-enhance the combined driver module to address the validation failures:

1. **FIX SYNTAX ERRORS**: Ensure all Python syntax is correct
2. **PRESERVE BOUNDARY MARKERS**: Maintain exact boundary marker format
3. **IMPROVE FUNCTIONAL CHANGES**: Make meaningful enhancements that pass validation
4. **SAFE EXCEPTION HANDLING**: Use only the approved exception handling patterns
5. **COMPLETE IMPORTS**: Include all necessary import statements

CONVERSION CONTEXT:
==================
Original {source_db}: {enhancement_context.get('original_source_statement', '')[:200]}...
AI Corrected {target_db}: {enhancement_context.get('ai_corrected_output', '')[:200]}...
Current {target_db}: {enhancement_context.get('current_target_output', '')[:200]}...
Deployment Error: {enhancement_context.get('deployment_error', '')}

SAFE EXCEPTION HANDLING PATTERNS:
=================================
✅ **USE ONLY THESE PATTERNS**:
```python
try:
    # your regex code
except:
    pass  # NO VARIABLES - NO SCOPE ISSUES

# OR

try:
    # your regex code
except re.error:
    pass  # NO VARIABLES - NO SCOPE ISSUES
```

❌ **NEVER USE**:
```python
except re.error as e:  # AVOID unless 'e' is used only within except block
```

BOUNDARY MARKER REQUIREMENTS:
============================
🚨 **MANDATORY STRUCTURE - COPY EXACTLY** 🚨:
```
# === MODULE: [module_name] START ===
[enhanced module code here]
# === MODULE: [module_name] END ===
```

**CRITICAL**: Every module MUST have both START and END markers preserved exactly.

ENHANCEMENT APPROACH:
====================
1. **ANALYZE**: What caused the validation failures
2. **PRESERVE**: All existing functionality completely
3. **ADD**: Only safe, validated enhancements
4. **TEST**: Ensure syntax correctness
5. **VERIFY**: Boundary markers are preserved exactly

OUTPUT FORMAT (JSON):
====================
{{
  "enhanced_code": "Complete re-enhanced combined module with validation issues fixed and ALL boundary markers preserved exactly",
  "analysis": "Explanation of: 1) What validation issues were identified and fixed, 2) How boundary markers were preserved, 3) What syntax errors were corrected, 4) How functional improvements were made",
  "code_changed": true
}}
"""
    
    return prompt


def create_execution_feedback_prompt(
    execution_error: str,
    pipeline_components: Dict[str, Any],
    enhanced_modules: List[Dict[str, Any]],
    enhancement_context: Dict[str, Any]
) -> str:
    """
    Create prompt for execution feedback to enhance_driver_module node.
    
    Args:
        execution_error: Execution error message
        pipeline_components: Pipeline execution components
        enhanced_modules: List of enhanced module information
        enhancement_context: Context for enhancement
        
    Returns:
        Formatted prompt for execution feedback enhancement
    """
    
    db_terms = enhancement_context.get('db_terms', {})
    source_db = db_terms.get('source_db', 'Source Database')
    target_db = db_terms.get('target_db', 'Target Database')
    
    prompt = f"""
You are re-enhancing a combined driver module due to EXECUTION FAILURES in the sequential enhancement pipeline.

EXECUTION FEEDBACK CONTEXT:
===========================
The enhanced modules passed validation but failed during pipeline execution:

EXECUTION ERROR:
{execution_error}

PIPELINE EXECUTION CONTEXT:
===========================
Pre-Features Output: {pipeline_components.get('pre_features_output', '')[:200]}...
Enhanced Modules Applied: {len(enhanced_modules)}
Post-Features: {pipeline_components.get('post_features', [])}

EXECUTION FAILURE ANALYSIS:
===========================
The enhanced modules caused runtime errors during pipeline execution. Common causes:
1. **REGEX RUNTIME ERRORS**: Invalid group references or malformed patterns
2. **MODULE EXECUTION ERRORS**: Errors in enhanced module logic
3. **IMPORT ERRORS**: Missing or incorrect import statements
4. **VARIABLE SCOPE ERRORS**: Variables referenced outside their scope

ENHANCED MODULES THAT FAILED EXECUTION:
======================================
"""
    
    for module_info in enhanced_modules:
        prompt += f"""
MODULE: {module_info['feature_name']}
ENHANCED CODE:
{module_info['enhanced_code'][:500]}...

"""
    
    prompt += f"""
EXECUTION FEEDBACK ENHANCEMENT TASK:
====================================
Re-enhance the combined driver module to fix execution failures:

1. **FIX RUNTIME ERRORS**: Address regex and execution errors
2. **VALIDATE GROUP REFERENCES**: Ensure regex group references are correct
3. **SAFE EXECUTION**: Use defensive programming practices
4. **ERROR HANDLING**: Implement robust error handling
5. **TESTING**: Ensure modules execute without errors

CONVERSION CONTEXT:
==================
Original {source_db}: {enhancement_context.get('original_source_statement', '')[:200]}...
AI Corrected {target_db}: {enhancement_context.get('ai_corrected_output', '')[:200]}...
Current {target_db}: {enhancement_context.get('current_target_output', '')[:200]}...
Deployment Error: {enhancement_context.get('deployment_error', '')}

COMMON EXECUTION ERROR FIXES:
=============================
1. **INVALID GROUP REFERENCE**: 
   - Problem: Using \\1, \\2 without corresponding capture groups
   - Fix: Ensure pattern has matching capture groups

2. **REGEX COMPILATION ERRORS**:
   - Problem: Malformed regex patterns
   - Fix: Test patterns before use

3. **VARIABLE SCOPE ERRORS**:
   - Problem: Exception variables used outside except blocks
   - Fix: Use simple except without variables

SAFE EXECUTION PATTERNS:
=======================
✅ **SAFE REGEX PATTERN**:
```python
try:
    if re.search(pattern, data, re.DOTALL|re.I):
        data = re.sub(pattern, replacement, data, flags=re.DOTALL|re.I)
except:
    pass  # Continue with original data
```

✅ **SAFE GROUP REFERENCE VALIDATION**:
```python
pattern = r'(capture_group_1)(.*?)(capture_group_2)'
replacement = r'\\3\\2\\1'  # Uses groups 1, 2, 3 - matches pattern
```

❌ **UNSAFE PATTERNS TO AVOID**:
```python
# WRONG: Group reference without capture group
pattern = r'no_capture_groups'
replacement = r'\\1'  # ERROR: invalid group reference

# WRONG: Variable scope error
try:
    data = re.sub(pattern, replacement, data)
except re.error as e:
    pass
print(e)  # ERROR: 'e' not defined outside except block
```

EXECUTION ERROR PREVENTION:
===========================
1. **VALIDATE PATTERNS**: Test regex patterns before applying
2. **MATCH GROUPS**: Ensure replacement groups match pattern groups
3. **DEFENSIVE CODING**: Use try/except for all regex operations
4. **SIMPLE EXCEPTIONS**: Avoid exception variables when possible
5. **FALLBACK LOGIC**: Return original data on any error

OUTPUT FORMAT (JSON):
====================
{{
  "enhanced_code": "Complete re-enhanced combined module with execution issues fixed and safe execution patterns",
  "analysis": "Explanation of: 1) What execution errors were identified and fixed, 2) How regex patterns were corrected, 3) What safety measures were added, 4) How runtime errors were prevented",
  "code_changed": true
}}
"""
    
    return prompt


def create_ai_comparison_feedback_prompt(
    ai_comparison_feedback: str,
    attempt_history: List[Dict[str, Any]],
    current_attempt: int,
    enhancement_context: Dict[str, Any]
) -> str:
    """
    Create prompt for AI comparison feedback to enhance_driver_module node.
    
    Args:
        ai_comparison_feedback: Feedback from AI statement comparison
        attempt_history: History of previous attempts
        current_attempt: Current attempt number
        enhancement_context: Context for enhancement
        
    Returns:
        Formatted prompt for AI comparison feedback enhancement
    """
    
    db_terms = enhancement_context.get('db_terms', {})
    source_db = db_terms.get('source_db', 'Source Database')
    target_db = db_terms.get('target_db', 'Target Database')
    
    # Build attempt history section
    history_section = ""
    if attempt_history:
        history_section = "PREVIOUS ATTEMPT HISTORY:\n"
        history_section += "=" * 40 + "\n"
        for i, attempt in enumerate(attempt_history, 1):
            history_section += f"\nATTEMPT {i}:\n"
            history_section += f"Enhanced Modules: {attempt.get('modules_used', [])}\n"
            history_section += f"Final Output: {attempt.get('final_output', '')[:200]}...\n"
            history_section += f"Feedback: {attempt.get('ai_comparison_feedback', '')[:200]}...\n"
    
    prompt = f"""
You are re-enhancing a combined driver module due to AI COMPARISON FAILURES in the sequential enhancement pipeline.

AI COMPARISON FEEDBACK CONTEXT:
===============================
Attempt {current_attempt} failed AI statement comparison. The AI analysis indicates:

{ai_comparison_feedback}

{history_section}

COMPARISON FAILURE ANALYSIS:
===========================
The enhanced modules produced output that doesn't match the expected AI corrected statement functionally.
This indicates the enhancement approach needs to be different from previous attempts.

LEARNING FROM PREVIOUS ATTEMPTS:
================================
Based on the attempt history, avoid repeating the same enhancement approaches that failed.
Try a different strategy for achieving the expected output.

CONVERSION CONTEXT:
==================
Original {source_db}: {enhancement_context.get('original_source_statement', '')[:200]}...
AI Corrected {target_db}: {enhancement_context.get('ai_corrected_output', '')[:200]}...
Current Pipeline Output: {enhancement_context.get('current_target_output', '')[:200]}...
Deployment Error: {enhancement_context.get('deployment_error', '')}

AI COMPARISON FEEDBACK ENHANCEMENT TASK:
========================================
Re-enhance the combined driver module based on AI comparison feedback:

1. **ANALYZE FEEDBACK**: Understand why the comparison failed
2. **DIFFERENT APPROACH**: Try a different enhancement strategy than previous attempts
3. **FUNCTIONAL EQUIVALENCE**: Focus on achieving functional equivalence with AI corrected output
4. **AVOID REPETITION**: Don't repeat failed approaches from attempt history
5. **TARGETED CHANGES**: Make specific changes based on the feedback

ENHANCEMENT STRATEGY GUIDELINES:
===============================
1. **UNDERSTAND THE GAP**: What specific differences exist between current and expected output?
2. **ROOT CAUSE ANALYSIS**: Why are the current enhancements not achieving the expected result?
3. **ALTERNATIVE APPROACH**: What different transformation logic could achieve the goal?
4. **INCREMENTAL CHANGES**: Make targeted changes rather than wholesale rewrites
5. **VALIDATION**: Ensure changes address the specific feedback points

FEEDBACK-DRIVEN ENHANCEMENT PRINCIPLES:
======================================
- **SPECIFIC**: Address the exact issues mentioned in the feedback
- **TARGETED**: Focus on the specific transformation gaps identified
- **DIFFERENT**: Use a different approach than previous failed attempts
- **FUNCTIONAL**: Ensure the enhancement achieves functional equivalence
- **SAFE**: Maintain safe coding practices and error handling

OUTPUT FORMAT (JSON):
====================
{{
  "enhanced_code": "Complete re-enhanced combined module with AI comparison feedback addressed and different approach than previous attempts",
  "analysis": "Explanation of: 1) How AI comparison feedback was analyzed, 2) What different approach was taken, 3) How this addresses the functional gaps, 4) Why this approach should succeed where previous attempts failed",
  "code_changed": true
}}
"""
    
    return prompt


def create_iteration_feedback_prompt(testing_result: Dict[str, Any], enhancement_context: Dict[str, Any]) -> str:
    """
    Create iteration feedback prompt based on testing results and enhancement context.

    This is the main function that determines which type of feedback prompt to create
    based on the testing result type (validation, execution, or AI comparison failure).

    Args:
        testing_result: Dictionary containing test results and failure information
        enhancement_context: Context for enhancement including conversion data

    Returns:
        Formatted prompt string for the specific type of feedback
    """

    # Determine the type of feedback needed based on testing result
    feedback_type = testing_result.get('feedback_type', 'ai_comparison')

    if feedback_type == 'validation':
        # Validation feedback - modules failed syntax/boundary validation
        validation_errors = testing_result.get('validation_errors', [])
        enhanced_modules = testing_result.get('enhanced_modules', [])
        combined_driver_module = enhancement_context.get('combined_driver_module', '')

        return create_validation_feedback_prompt(
            validation_errors, enhanced_modules, combined_driver_module, enhancement_context
        )

    elif feedback_type == 'execution':
        # Execution feedback - modules failed during pipeline execution
        execution_error = testing_result.get('execution_error', '')
        pipeline_components = testing_result.get('pipeline_components', {})
        enhanced_modules = testing_result.get('enhanced_modules', [])

        return create_execution_feedback_prompt(
            execution_error, pipeline_components, enhanced_modules, enhancement_context
        )

    elif feedback_type == 'ai_comparison':
        # AI comparison feedback - output doesn't match expected result
        ai_comparison_feedback = testing_result.get('ai_comparison_feedback', '')
        attempt_history = testing_result.get('attempt_history', [])
        current_attempt = testing_result.get('current_attempt', 1)

        return create_ai_comparison_feedback_prompt(
            ai_comparison_feedback, attempt_history, current_attempt, enhancement_context
        )

    else:
        # Default to AI comparison feedback if type is unknown
        ai_comparison_feedback = testing_result.get('feedback', '')
        attempt_history = testing_result.get('attempt_history', [])
        current_attempt = testing_result.get('current_attempt', 1)

        return create_ai_comparison_feedback_prompt(
            ai_comparison_feedback, attempt_history, current_attempt, enhancement_context
        )
