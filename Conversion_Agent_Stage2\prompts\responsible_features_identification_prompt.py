"""
Prompts for identifying responsible features in Stage 2 conversion analysis.
"""
from typing import Dict, List, Any, Optional
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms


def create_responsible_features_identification_prompt(
    original_source: str,
    ai_converted: str,
    actual_target: str,
    deployment_error: str,
    decrypted_modules: Dict[str, str],
    keyword_mapping: List[Dict[str, Any]],
    available_features: List[tuple],
    validation_feedback: Optional[str] = None,
    db_terms: Optional[Dict[str, str]] = None
) -> str:
    """
    Create AI analysis prompt for identifying responsible modules (Stage 2 style with dynamic database names).

    Args:
        original_source: Original source database statement
        ai_converted: Expected target database statement from AI
        actual_target: Actual wrong target database statement
        deployment_error: Error message from deployment
        decrypted_modules: Dictionary of decrypted Python module contents
        keyword_mapping: Keyword-to-module mapping from CSV
        available_features: List of available feature tuples
        validation_feedback: Optional feedback from previous validation attempts
        db_terms: Database-specific terms from migration name (Request-First Approach)

    Returns:
        Formatted prompt string for AI analysis with structured output
    """

    # Use provided database terms or fallback to config-based approach
    if db_terms is None:
        db_terms = get_database_specific_terms()

    source_db = db_terms['source_db']
    target_db = db_terms['target_db']
    migration_direction = db_terms['migration_direction']
    expert_title = db_terms['expert_title']

    # Format available features for prompt
    available_features_str = "\n".join([f"- {name}: {path}" for name, path in available_features])

    # Include validation feedback if available
    feedback_section = ""
    if validation_feedback:
        feedback_section = f"""
VALIDATION FEEDBACK FROM PREVIOUS ATTEMPT:
=========================================
{validation_feedback}

Use this feedback to improve your identification accuracy. Focus on the specific guidance provided.

"""

    prompt = f"""
You are a {expert_title}. Your task is to identify which specific Python modules from the available features are responsible for {migration_direction} conversion failures.

{feedback_section}CONVERSION ANALYSIS:
===================

Original {source_db} Statement:
{original_source}

AI Corrected {target_db} Statement (Expected Output):
{ai_converted}

QMigrator {target_db} Statement (Current Output):
{actual_target}

Deployment Error:
{deployment_error}

AVAILABLE FEATURES TO ANALYZE:
=============================
{available_features_str}

DECRYPTED MODULE CODE:
=====================
"""
    
    for module_name, module_code in decrypted_modules.items():
        prompt += f"\n--- {module_name.upper()} MODULE ---\n{module_code}\n"

    prompt += f"""

KEYWORD MAPPING REFERENCE:
=========================
{keyword_mapping}

ANALYSIS TASK:
=============
1. **PRIMARY GOAL**: Identify which QMigrator modules failed to produce the AI-corrected output
2. Compare AI Corrected {target_db} vs QMigrator {target_db} to find conversion failures
3. **CRITICAL**: Analyze the Deployment Error to understand the exact failure cause and impact
4. Review each available feature's decrypted code to understand conversion logic
5. Focus on modules that should have handled specific {source_db} syntax but failed
6. **ESSENTIAL**: Link each responsible module directly to the specific deployment error
7. **SOLUTION GUIDANCE**: Provide actionable solution patterns that handle multiple scenarios:
   - Include transformation patterns based on the specific differences identified
   - Account for whitespace, case, and formatting variations
   - Provide step-by-step implementation guidance
   - Make solutions adaptable to similar transformation scenarios

IDENTIFICATION METHODOLOGY:
==========================
1. **Error Analysis**: Examine deployment error for specific syntax/keyword failures and root cause
2. **Statement Comparison**: Compare AI Corrected vs QMigrator output for differences
3. **Module Mapping**: Match {source_db} keywords to responsible conversion modules
4. **Code Review**: Analyze module code to understand conversion logic and potential failures
5. **Error-Module Linking**: Establish direct connection between deployment error and module responsibility

IDENTIFICATION CRITERIA:
=======================
**PRIMARY RULE**: Only identify modules DIRECTLY responsible for the specific deployment error.

**REQUIRED CONDITIONS** (ALL must be met):
1. Module handles EXACT keywords mentioned in deployment error
2. Module's primary purpose is converting the failing syntax element
3. Module's current code lacks logic for this specific error scenario
4. Module is responsible for the specific transformation that failed
5. **CRITICAL**: Module's failure directly caused the deployment error

**EXCLUSION RULES**:
- Do NOT identify modules just because they appear in the statement
- Do NOT identify modules that work correctly but happen to be present
- Do NOT identify modules whose primary purpose differs from the error cause

**SOLUTION PATTERN REQUIREMENTS**:
For each identified responsible module, provide comprehensive solution guidance:
- **Multiple Pattern Examples**: Show how to handle different variations of the transformation needed
- **Flexible Matching**: Include patterns that handle whitespace, case, and bracket variations
- **Implementation Steps**: Provide clear step-by-step guidance for implementing the transformation
- **Variation Coverage**: Account for different formatting scenarios the module might encounter
- **Generic Approach**: Make patterns adaptable to similar constructs without hardcoding specific keywords

OUTPUT FORMAT (JSON):
====================
{{
  "responsible_features": [
    {{
      "feature_name": "<feature_name>",
      "module_path": "<relative_path>",
      "responsibility_reason": "<DEPLOYMENT_ERROR_CONTEXT: Quote the specific deployment error> <ERROR_CAUSE: Explain why this error occurred> <MODULE_ROLE: How this module contributed to the error> <EXPECTED_FIX: What the module should do to resolve the error> <SOLUTION_PATTERNS: Provide multiple example patterns that handle variations - include different whitespace, case, and bracket variations> <IMPLEMENTATION_GUIDANCE: Step-by-step approach to implement the fix>",
      "error_impact": "High|Medium|Low",
      "keywords_matched": ["<keyword1>", "<keyword2>"],
      "confidence_score": <float between 0.0 and 1.0>
    }}
  ],
  "analysis_summary": "<comprehensive explanation covering ALL responsible features, analysis process, comparison findings, and detailed reasoning>"
}}

**RESPONSIBILITY_REASON FORMAT REQUIREMENTS**:
The responsibility_reason field MUST follow this structure:
1. **DEPLOYMENT_ERROR_CONTEXT**: Quote the exact deployment error message
2. **ERROR_CAUSE**: Explain the root cause of why this error occurred
3. **MODULE_ROLE**: Describe how this specific module contributed to the error
4. **EXPECTED_FIX**: Specify what the module should do to resolve the deployment error
5. **SOLUTION_PATTERNS**: Provide multiple example patterns that handle variations:
   - Pattern 1: Handle exact case (source_pattern → target_pattern)
   - Pattern 2: Handle whitespace variations (source_with_spaces → target_pattern)
   - Pattern 3: Handle case variations (source_different_case → target_pattern)
   - Pattern 4: Handle nested/complex variations (source_with_newlines → target_pattern)
6. **IMPLEMENTATION_GUIDANCE**: Step-by-step approach to implement the fix:
   - Step 1: Identify pattern using flexible regex with \\s* and re.IGNORECASE
   - Step 2: Extract variable components using capture groups
   - Step 3: Apply transformation template with extracted components
   - Step 4: Validate result matches expected pattern structure

**GENERIC EXAMPLE FORMAT**:
"DEPLOYMENT_ERROR_CONTEXT: [Quote the exact deployment error] ERROR_CAUSE: [Explain why this error occurred based on output differences] MODULE_ROLE: [Describe how this module contributed to the error] EXPECTED_FIX: [What the module should do to resolve the error] SOLUTION_PATTERNS: [Pattern 1: source_pattern → target_pattern, Pattern 2: source_with_spaces → target_pattern, Pattern 3: source_different_case → target_pattern, Pattern 4: source_with_newlines → target_pattern] IMPLEMENTATION_GUIDANCE: [Step 1: Use flexible regex with \\s* and re.IGNORECASE, Step 2: Extract variable components, Step 3: Apply transformation template, Step 4: Validate transformation success]"



VALIDATION APPROACH:
==================
For each potential module, verify:
1. Is this module's PRIMARY purpose to handle the failing syntax element?
2. Does the deployment error directly relate to this module's conversion logic?
3. Would fixing ONLY this module resolve the specific deployment error?
4. **CRITICAL**: Can you trace a direct path from the deployment error to this module's responsibility?
5. **ESSENTIAL**: Does the responsibility_reason clearly explain the deployment error context?

**Key Principle**: Be PRECISE and SPECIFIC. Quality over quantity - identify fewer, more accurate modules.

**DEPLOYMENT ERROR FOCUS**: Every responsibility_reason MUST reference the specific deployment error and explain how the module caused it.

SOLUTION PATTERN QUALITY REQUIREMENTS:
=====================================
Ensure each responsibility_reason provides:
1. **ACTIONABLE PATTERNS**: Specific transformation examples based on the actual differences found
2. **VARIATION COVERAGE**: Handle whitespace, case, bracket, and formatting differences
3. **FLEXIBLE REGEX**: Use patterns like \\s*, \\s+, re.IGNORECASE for robust matching
4. **GENERIC APPROACH**: Make patterns adaptable to similar constructs without hardcoding specific keywords
5. **IMPLEMENTATION READY**: Provide step-by-step guidance that developers can follow
6. **MULTIPLE SCENARIOS**: Include 3-4 pattern variations to handle different formatting
7. **VALIDATION CRITERIA**: Specify how to verify the transformation worked correctly

CRITICAL SUCCESS FACTORS:
========================
- **NO HARDCODED KEYWORDS**: Use flexible patterns that adapt to any SQL construct variations
- **COMPREHENSIVE COVERAGE**: Handle all common formatting variations
- **CLEAR GUIDANCE**: Provide specific implementation steps
- **TESTABLE PATTERNS**: Include validation criteria for success verification
- **ADAPTIVE APPROACH**: Focus on transformation patterns, not specific SQL keywords

Focus ONLY on identifying responsible modules. Do NOT suggest code changes.
"""
    
    return prompt