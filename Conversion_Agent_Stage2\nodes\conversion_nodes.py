# Standard library imports
import os
import pandas as pd
import re
from typing import Dict, Any, List, Optional
import datetime, shutil
import importlib.util
from datetime import datetime

# Local imports - State
from Conversion_Agent_Stage2.state import (
    Stage2WorkflowState,
    ResponsibleFeaturesAnalysisOutput,
    ModuleEnhancementOutput,
    StatementComparisonOutput
)
from config import Config
from Conversion_Agent_Stage2.qmigrator_conversion.object_conversion import qbook_object_conversion, decrypt_conversion_file, replace_comment_markers
from Conversion_Agent_Stage2.prompts.responsible_features_identification_prompt import (
    create_responsible_features_identification_prompt
)


from Conversion_Agent_Stage2.prompts.module_enhancement_prompt import (
    create_module_enhancement_prompt
)
from Conversion_Agent_Stage2.prompts.sequential_enhancement_prompt import (
    create_sequential_enhancement_prompt
)
from Conversion_Agent_Stage2.utils.database_names import get_database_specific_terms
from Conversion_Agent_Stage2.prompts.iteration_feedback_prompt import (
    create_iteration_feedback_prompt
)

from Conversion_Agent_Stage2.utils.database_names import (
    get_database_specific_terms
)
from Conversion_Agent_Stage2.prompts.ai_statement_comparison_prompt import (
    create_ai_statement_comparison_prompt
)
from Conversion_Agent_Stage2.utils.driver_module_enhancement import DriverModuleEnhancer

def get_stage2_excel_path(metadata_dir: str, schema_name: str, object_name: str) -> str:
    """Get the path for the Stage 2 workflow tracking Excel file."""
    filename = f"{schema_name}_{object_name}_Stage2.xlsx"
    return os.path.join(metadata_dir, filename)

def create_excel_with_sheet(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> str:
    """
    Generic function to create new Excel file with first sheet.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to create
        data: List of dictionaries containing sheet data

    Returns:
        str: Path to the created Excel file
    """
    try:
        df = pd.DataFrame(data)
        with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
            df.to_excel(writer, sheet_name=sheet_name, index=False)
        return file_path
    except Exception as e:
        print(f"❌ Error creating Excel file {file_path}: {e}")
        return ""

def append_sheet_to_excel(file_path: str, sheet_name: str, data: List[Dict[str, Any]]) -> bool:
    """
    Generic function to append data to existing Excel sheet without reading full content.

    Args:
        file_path: Full path to Excel file
        sheet_name: Name of the sheet to append
        data: List of dictionaries containing sheet data

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        df = pd.DataFrame(data)

        if not os.path.exists(file_path):
            # Create new file
            with pd.ExcelWriter(file_path, mode='w', engine='openpyxl') as writer:
                df.to_excel(writer, sheet_name=sheet_name, index=False)
        else:
            # Append to existing file - add rows to the bottom of existing sheet
            with pd.ExcelWriter(file_path, mode='a', if_sheet_exists='overlay', engine='openpyxl') as writer:
                # Check if sheet exists to determine start row
                if sheet_name in writer.book.sheetnames:
                    existing_sheet = writer.book[sheet_name]
                    start_row = existing_sheet.max_row  # Start after last row
                    df.to_excel(writer, sheet_name=sheet_name, index=False, header=False, startrow=start_row)
                else:
                    # Sheet doesn't exist, create new with headers
                    df.to_excel(writer, sheet_name=sheet_name, index=False)

        return True
    except Exception as e:
        print(f"❌ Error appending sheet {sheet_name} to {file_path}: {e}")
        return False

class Stage2ProcessingNodes:
    """
    Stage 2 processing nodes for QMigrator module updates.

    This class provides all workflow nodes for the complete Stage 2 processing
    with a streamlined 9-node pipeline approach. Handles both qmigrator (object-level)
    and qbook (statement-level) processing with comprehensive retry mechanisms.

    Core Workflow Nodes:
        1. Process Type Decision - Routes between qmigrator/qbook paths
        2. Post Stage1 Processing (QMigrator) - Object-level processing
        3. Map Feature Combinations (QMigrator) - Feature mapping
        4. Statement Level Processing (QBook) - Statement-level processing
        5. Identify Responsible Features - AI-driven feature identification

    9-Node Pipeline (Enhanced Module Processing):
        6. Categorize Execution Modules - Pre/responsible/post categorization
        7. Execute Pre Features - Pre-processing execution
        8. Combine Driver Module - Module combination
        9. Enhance Driver Module - AI-driven enhancement
        10. Decompose Enhanced Module - Module decomposition
        11. Validate Module Enhancement - Validation with retry
        12. Execute Complete Pipeline - Full pipeline execution with retry
        13. AI Statement Comparison - Functional equivalence analysis
        14. Enhancement Iteration Control - Retry coordination

    Additional Nodes:
        - More Statements Decision - Statement processing loop control
        - Complete Processing - Workflow finalization

    Workflow Integration:
        Designed for use with LangGraph workflow orchestration, providing seamless
        state management and conditional routing based on validation results.
        Includes comprehensive Excel logging and retry mechanisms.
    """

    def __init__(self, llm):
        """
        Initialize the Stage 2 processing nodes with AI language model integration.

        Sets up the node collection with the provided language model for AI-driven
        analysis throughout the Stage 2 module update workflow.

        Args:
            llm: Initialized Language Model instance supporting structured outputs
                 for reliable AI analysis and module update operations. Compatible with
                 multiple providers (OpenAI, Azure OpenAI, Anthropic, Groq, Gemini, Ollama).

        Attributes:
            llm: The language model instance used across all Stage 2 nodes
        """
        self.llm = llm

    # ==================== WORKFLOW DECISION NODES ====================

    def process_type_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 1: Process Type Decision - Workflow Router

        Purpose:
            Routes the workflow to appropriate processing path based on the process_type parameter.
            This is the entry point that determines whether to use QMigrator (object-level) or
            QBook (statement-level) processing approach.

        Business Logic:
            - QMigrator: Object-level processing for complete database objects (procedures, functions)
            - QBook: Statement-level processing for individual SQL statements

        Input Requirements:
            - state.process_type: Must be either "qmigrator" or "qbook"

        Output:
            - process_type: Confirmed process type for downstream routing

        Next Nodes:
            - qmigrator → post_stage1_processing_qmigrator
            - qbook → statement_level_processing_qbook

        Error Handling:
            - No validation errors expected as process_type is set during workflow initialization
        """
        print(f"🔀 Process Type Decision: {state.process_type}")

        if state.process_type == "qmigrator":
            print("📁 Routing to QMigrator object-level processing")
        else:
            print("📝 Routing to QBook statement-level processing")

        # Return state update - routing is handled by conditional edges
        return {
            "process_type": state.process_type
        }

    # ==================== QMIGRATOR PATH NODES ====================

    def post_stage1_processing_qmigrator(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 2: Post-Stage 1 Processing - QMigrator Object Conversion

        Purpose:
            Processes Stage 1 approved statements through QMigrator object-level conversion.
            This node handles complete database objects (procedures, functions, triggers)
            by reading approved statements and running comprehensive QMigrator conversion.

        Business Logic:
            1. Reads approved_statements.csv from Stage 1 metadata directory
            2. Loads source_code.sql containing the original object code
            3. Executes QMigrator object-level conversion with full feature processing
            4. Generates conversion results including statements, features, and deployment errors

        Input Requirements:
            - state.migration_name: Migration identifier for path construction
            - state.schema_name: Database schema name
            - state.object_name: Database object name
            - state.objecttype: Object type (procedure, function, trigger, etc.)
            - state.cloud_category: Deployment environment (cloud/local)

        Output:
            - qmigrator_results_df: DataFrame with conversion results
            - approved_statements_df: DataFrame with approved statements
            - source_code: Original SQL source code
            - conversion_success: Boolean indicating conversion status

        Next Nodes:
            - Success → map_feature_combinations
            - Failure → complete_processing (with error)

        Error Handling:
            - File not found errors for missing Stage 1 outputs
            - QMigrator conversion failures
            - DataFrame processing errors
        """
        print("🔄 Starting Post-Stage 1 Processing (QMigrator)...")
        print(f"🔧 Migration: {state.migration_name}")
        print(f"🏗️ Schema: {state.schema_name}, Object: {state.object_name}, Type: {state.objecttype}")

        try:
            # Clean process-type-specific feature modules directory for fresh start when reprocessing
            self.cleanup_process_feature_modules(state)
            # Determine paths based on cloud_category
            if state.cloud_category.lower() == "local":
                qbook_root_path = Config.Qbook_Local_Path
                temp_root_path = Config.Temp_Local_Path
            else:
                qbook_root_path = Config.Qbook_Path
                temp_root_path = Config.Temp_Path

            # Construct metadata directory path
            metadata_dir = os.path.join(
                qbook_root_path,
                'Stage1_Metadata',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )

            # Create temp directory for Stage 2 processing
            temp_dir = os.path.join(
                temp_root_path,
                'Stage2_Processing',
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name
            )
            os.makedirs(temp_dir, exist_ok=True)

            approved_statements_path = os.path.join(metadata_dir, 'approved_statements.csv')
            source_code_path = os.path.join(metadata_dir, 'source_code.sql')

            print(f"📁 Metadata directory: {metadata_dir}")

            # 3. Read approved statements CSV
            if not os.path.exists(approved_statements_path):
                raise FileNotFoundError(f"Approved statements file not found: {approved_statements_path}")

            approved_statements_df = pd.read_csv(approved_statements_path)
            print(f"📊 Loaded {len(approved_statements_df)} approved statements")

            # 4. Read source code file
            if not os.path.exists(source_code_path):
                raise FileNotFoundError(f"Source code file not found: {source_code_path}")

            with open(source_code_path, 'r', encoding='utf-8') as f:
                source_code_content = f.read()

            if not source_code_content.strip():
                raise ValueError("Source code file is empty")

            print(f"📝 Loaded source code ({len(source_code_content)} characters)")

            # 5. Run QMigrator object-level conversion
            print("🔄 Running QMigrator object-level conversion...")

            object_converted_output, available_features_df, comment_dict = qbook_object_conversion(
                migration_name=state.migration_name,
                schema_name=state.schema_name,
                object_type=state.objecttype,
                object_name=state.object_name,
                source_data=source_code_content,
                cloud_category=state.cloud_category
            )
            
            # print(object_converted_output, 'object_converted_output=======================')

            if object_converted_output is None:
                raise ValueError("QMigrator conversion failed - no output generated")

            print(f"✅ QMigrator conversion completed")
            print(f"📊 Available features: {len(available_features_df)} statements")

            # Create Stage 2 Excel tracking file in temp directory
            stage2_excel_path = get_stage2_excel_path(temp_dir, state.schema_name, state.object_name)
            final_qbook_excel_path = get_stage2_excel_path(metadata_dir, state.schema_name, state.object_name)
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            # Prepare data for Excel sheets
            source_code_data = [{
                "Migration_Name": state.migration_name,
                "Schema_Name": state.schema_name,
                "Object_Name": state.object_name,
                "Object_Type": state.objecttype,
                "Source_Code": source_code_content,
                "Timestamp": current_timestamp
            }]

            approved_statements_data = []
            for idx, row in approved_statements_df.iterrows():
                approved_statements_data.append({
                    "Migration_Name": row.get('migration_name', state.migration_name),
                    "Schema_Name": row.get('schema_name', state.schema_name),
                    "Object_Name": row.get('object_name', state.object_name),
                    "Object_Type": row.get('object_type', state.objecttype),
                    "TGT_Object_ID": row.get('tgt_object_id', ''),
                    "Source_Statement_Number": row.get('source_statement_number', ''),
                    "Target_Statement_Number": row.get('target_statement_number', ''),
                    "Original_Source_Statement": str(row.get('original_source_statement', '')),
                    "Original_Target_Statement": str(row.get('original_target_statement', '')),
                    "AI_Converted_Statement": str(row.get('ai_converted_statement', '')),
                    "Original_Deployment_Error": str(row.get('original_deployment_error', '')),
                    "Timestamp": current_timestamp
                })

            available_features_data = available_features_df.copy()
            available_features_data['Timestamp'] = current_timestamp
            available_features_list = available_features_data.to_dict('records')

            # Create Excel file with three sheets
            create_excel_with_sheet(stage2_excel_path, "Source_Code", source_code_data)
            append_sheet_to_excel(stage2_excel_path, "Approved_Statements", approved_statements_data)
            append_sheet_to_excel(stage2_excel_path, "Available_Features", available_features_list)
            print(f"📋 Sheet 'Available_Features' added with {len(available_features_list)} records")

            print("✅ Post-Stage 1 Processing (QMigrator) completed successfully")
            print(f"📊 Total approved statements: {len(approved_statements_df)}")
            print(f"📊 Total available features: {len(available_features_df)}")

            # 7. Return state fields for LangGraph persistence
            return {
                "approved_statements": approved_statements_df.to_dict('records'),
                "object_level_features": available_features_df.to_dict('records'),
                "source_code": source_code_content,
                "stage2_excel_path": stage2_excel_path,
                "final_qbook_excel_path": final_qbook_excel_path,
                "metadata_dir": metadata_dir,
                "temp_dir": temp_dir,
                "approved_statements_df": approved_statements_df,
                "available_features_df": available_features_df,
                "object_converted_output": object_converted_output,
                "comments_dict": comment_dict
            }

        except FileNotFoundError as e:
            error_msg = f"❌ File not found: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements": [],
                "object_level_features": [],
                "source_code": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": "",
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "object_converted_output": "",
                "comments_dict": {}
            }

        except Exception as e:
            error_msg = f"❌ Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "approved_statements_df": pd.DataFrame(),
                "available_features_df": pd.DataFrame(),
                "comments_dict": {},
                "source_code": "",
                "object_converted_output": "",
                "stage2_excel_path": "",
                "final_qbook_excel_path": "",
                "metadata_dir": "",
                "temp_dir": ""
            }

    def map_feature_combinations(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 3: Map Feature Combinations Node (QMigrator Only).

        Purpose: Map approved statements with object-level features based on statement number matching.
        Only creates combined entries when statement numbers match between approved statements and available features.

        Process:
            1. Get approved statements and available features from workflow state
            2. Map statements only when source_statement_number matches Statement_Number
            3. Create combined dataset for feature analysis with matching entries only
            4. Create Excel sheet with combined data

        Args:
            state: Stage2WorkflowState containing approved_statements and object_level_features

        Returns:
            Dict containing available_features_with_statements and excel_path
        """
        print("🔄 Starting Map Feature Combinations...")

        try:
            # Get DataFrames from workflow state
            if not state.approved_statements or not state.object_level_features:
                raise ValueError("Missing approved statements or object level features from previous step")

            # Convert state data back to DataFrames for processing
            approved_statements_df = pd.DataFrame(state.approved_statements)
            available_features_df = pd.DataFrame(state.object_level_features)

            print(f"📊 Processing {len(approved_statements_df)} approved statements")
            print(f"📊 Processing {len(available_features_df)} available features")

            # Create combined dataset only for matching statement numbers
            combined_features = []
            statement_id_counter = 1

            for _, approved_stmt in approved_statements_df.iterrows():
                # Find matching features by statement number
                source_stmt_num = approved_stmt.get('source_statement_number', 0)
                matching_features = available_features_df[
                    available_features_df['Statement_Number'] == source_stmt_num
                ]

                # Only create combined entry if matching features exist
                if not matching_features.empty:
                    feature_row = matching_features.iloc[0]
                    combined_entry = {
                        # Combined dataset ID
                        "statement_id": statement_id_counter,

                        # From approved statements
                        "migration_name": approved_stmt.get('migration_name', ''),
                        "schema_name": approved_stmt.get('schema_name', ''),
                        "object_name": approved_stmt.get('object_name', ''),
                        "object_type": approved_stmt.get('object_type', ''),
                        "tgt_object_id": approved_stmt.get('tgt_object_id', 0),
                        "source_statement_number": approved_stmt.get('source_statement_number', 0),
                        "target_statement_number": approved_stmt.get('target_statement_number', 0),
                        "original_source_statement": approved_stmt.get('original_source_statement', ''),
                        "original_target_statement": approved_stmt.get('original_target_statement', ''),
                        "ai_converted_statement": approved_stmt.get('ai_converted_statement', ''),
                        "original_deployment_error": approved_stmt.get('original_deployment_error', ''),

                        # From available features
                        "statement_after_typecasting": feature_row.get('Statement_After_Typecasting', ''),
                        "statement_level_output": feature_row.get('Statement_Level_Output', ''),
                        "available_features": feature_row.get('Available_Features', []),
                        "post_features": feature_row.get('Post_Features', [])
                    }
                    combined_features.append(combined_entry)
                    statement_id_counter += 1
                else:
                    print(f"⚠️ No matching features found for approved statement {source_stmt_num} - excluding from combined dataset")

            # Add Combined_Features sheet to existing Excel file using existing function
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                # Prepare combined data with timestamp
                combined_data = []
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                if combined_features:
                    for feature in combined_features:
                        combined_record = feature.copy()
                        combined_record['Timestamp'] = current_timestamp
                        combined_data.append(combined_record)
                else:
                    # Add empty record if no combined features
                    combined_data = [{"Message": "No matching features found", "Timestamp": current_timestamp}]

                # Use existing append_sheet_to_excel function
                append_sheet_to_excel(state.stage2_excel_path, "Combined_Features", combined_data)
                print(f"📋 Sheet 'Combined_Features' added with {len(combined_data)} records")
            else:
                print("⚠️ Stage 2 Excel file path not found in state - cannot add Combined_Features sheet")

            print("✅ Map Feature Combinations completed successfully")
            print(f"📊 Total matched combinations: {len(combined_features)}")

            return {
                "available_features_with_statements": combined_features
            }

        except Exception as e:
            error_msg = f"❌ Map Feature Combinations failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "available_features_with_statements": []
            }
    # ==================== QBOOK PATH NODES ====================

    def statement_level_processing_qbook(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 2: Statement Level Processing Node (QBook Path).

        Purpose: Process individual statements using QBook statement-level conversion.

        Process:
            Run QMigrator statement-level conversion on single statement
            Return converted statement for workflow processing

        Args:
            state: Stage2WorkflowState containing statement context

        Returns:
            Dict containing converted statement results
        """
        print("🔄 Starting Statement Level Processing (QBook)...")

        try:
            # TODO: Implement QBook statement-level processing
            # Get individual statement from state
            # Run QBook conversion for single statement
            # Return converted statement results

            print("✅ Statement Level Processing (QBook) completed successfully")

            return {
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

        except Exception as e:
            error_msg = f"❌ Statement Level Processing (QBook) failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "converted_statement": "",
                "responsible_features": [],
                "responsible_procedures": []
            }

    # ==================== FEATURE IDENTIFICATION NODES ====================

    def identify_responsible_features(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 5: Identify Responsible Features - AI-Driven Root Cause Analysis

        Purpose:
            Uses AI analysis to identify specific Python conversion modules responsible for
            conversion failures by comparing expected vs actual conversion results.
            This is the critical diagnostic node that determines which modules need enhancement.

        Business Logic:
            1. Analyzes conversion discrepancies between AI output and expected target
            2. Reads and decrypts available Python conversion modules
            3. Uses AI to correlate conversion failures with specific module responsibilities
            4. Maps Oracle-to-PostgreSQL keywords to identify relevant modules
            5. Provides detailed responsibility reasoning for each identified module

        AI Analysis Process:
            - Compares original_source_statement vs ai_converted_statement vs original_target_statement
            - Identifies functional gaps and conversion errors
            - Maps errors to specific module capabilities using keyword analysis
            - Provides actionable responsibility reasons for module enhancement

        Input Requirements:
            - state.available_features_with_statements: Combined statement and feature data
            - state.current_statement_index: Current statement being processed (0-based)
            - state.migration_name: For module path construction
            - state.cloud_category: For environment-specific paths

        Output:
            - responsible_features: List of (feature_name, module_path, responsibility_reason, keywords)
            - analysis_summary: AI analysis summary
            - keyword_matches: Keyword-to-module mappings found

        Next Nodes:
            - Success → categorize_execution_modules (9-node pipeline entry)
            - No features found → complete_processing

        Error Handling:
            - Missing statement data validation
            - Module decryption failures
            - AI analysis errors with fallback responses
        """
        print("🔄 Starting Identify Responsible Features...")

        try:
            # Get combined data and current statement index from state
            if not state.available_features_with_statements:
                raise ValueError("Missing available_features_with_statements from previous step")

            combined_data = state.available_features_with_statements
            current_statement_index = getattr(state, 'current_statement_index', 0)

            print(f"📊 Processing statement {current_statement_index + 1}/{len(combined_data)}")

            # Get the current statement to process
            if current_statement_index >= len(combined_data):
                raise ValueError(f"Current statement index {current_statement_index} exceeds available statements {len(combined_data)}")

            current_statement_data = combined_data[current_statement_index]
            statement_id = current_statement_data.get('statement_id', current_statement_index + 1)
            source_stmt_num = current_statement_data.get('source_statement_number', 'Unknown')

            print(f"🔍 Processing Current Statement:")
            print(f"   📝 Statement ID: {statement_id}")
            print(f"   📝 Source Statement Number: {source_stmt_num}")
            print(f"   📝 Position: {current_statement_index + 1}/{len(combined_data)}")

            # Extract key statement information for analysis
            original_source = current_statement_data.get('original_source_statement', '')
            ai_converted = current_statement_data.get('ai_converted_statement', '')
            actual_target = current_statement_data.get('original_target_statement', '')
            deployment_error = current_statement_data.get('original_deployment_error', '')

            print(f"🔍 Oracle Source: {original_source[:100]}...")
            print(f"🎯 Expected PostgreSQL: {ai_converted[:100]}...")
            print(f"❌ Actual PostgreSQL: {actual_target[:100]}...")
            if deployment_error:
                print(f"⚠️ Deployment Error: {deployment_error[:100]}...")

            # Get migration name and cloud category from state (Request-First Approach)
            migration_name = state.migration_name
            cloud_category = getattr(state, 'cloud_category', 'cloud')  # Default to 'cloud' if not found
            print(f"🔧 Using dynamic migration name: {migration_name}")
            print(f"☁️ Using cloud category: {cloud_category}")

            # Load keyword mapping dynamically based on migration_name and cloud_category
            keyword_mapping = self.load_keyword_mapping(migration_name, cloud_category)

            # Get module paths from available_features for this statement
            available_features = current_statement_data.get("available_features", [])
            print(f"📦 Available features for analysis: {len(available_features)} modules")

            module_paths = self.get_module_paths(
                available_features,
                current_statement_data.get("migration_name", migration_name),
                cloud_category
            )

            # Decrypt and read Python modules for this statement
            decrypted_modules = self.decrypt_and_read_modules(module_paths)
            print(f"🔓 Decrypted {len(decrypted_modules)} modules for analysis")

            # Get validation feedback if available
            validation_feedback = getattr(state, 'validation_feedback', None)
            if validation_feedback:
                print(f"🔄 Using validation feedback for improved identification...")
                print(f"📝 Feedback: {validation_feedback[:100]}...")

            # AI analysis to identify responsible modules for this specific statement
            print(f"🧠 Starting AI analysis for current statement...")
            analysis_result = self.ai_analyze_responsible_modules(
                current_statement_data, decrypted_modules, keyword_mapping, migration_name, validation_feedback
            )

            # Extract results
            responsible_features = analysis_result.get("responsible_features", [])
            analysis_summary = analysis_result.get("analysis_summary", "No analysis summary available")

            # Log results for this statement
            print(f"✅ Current statement analysis complete:")
            print(f"   📊 Responsible modules found: {len(responsible_features)}")

            if responsible_features:
                module_names = [feature[0] for feature in responsible_features]
                print(f"   🎯 Modules: {', '.join(module_names)}")
            else:
                print("   ✅ No responsible modules - conversion working correctly")

            # Log to Excel (optional - for tracking purposes)
            if hasattr(state, 'stage2_excel_path') and state.stage2_excel_path:
                current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

                # Format responsible features for Excel logging with keywords
                features_for_excel = []
                for feature_data in responsible_features:
                    if len(feature_data) == 4:
                        feature_name, module_path, responsibility_reason, keywords = feature_data
                        features_for_excel.append(f"{feature_name} | {module_path} | Responsibility: {responsibility_reason[:50]}... | Keywords: {keywords}")
                    elif len(feature_data) == 3:
                        feature_name, module_path, responsibility_reason = feature_data
                        features_for_excel.append(f"{feature_name} | {module_path} | Responsibility: {responsibility_reason[:50]}... | Keywords: N/A")
                    elif len(feature_data) >= 2:
                        feature_name, module_path = feature_data[0], feature_data[1]
                        features_for_excel.append(f"{feature_name} | {module_path} | Responsibility: N/A | Keywords: N/A")
                    else:
                        features_for_excel.append(f"Invalid feature data: {feature_data}")

                # Create comprehensive log entry with detailed analysis
                log_entry = {
                    "Statement_ID": statement_id,
                    "Source_Statement_Number": source_stmt_num,
                    "Responsible_Modules_Count": len(responsible_features),
                    "Responsible_Features": features_for_excel,  # Include keywords in readable format
                    "Responsible_Features_Raw": responsible_features,  # Keep original tuples for processing
                    "Analysis": analysis_summary,  # Detailed explanation of analysis process and findings
                    "Processing_Attempt": getattr(state, 'current_attempt', 1),
                    "Timestamp": current_timestamp
                }

                append_sheet_to_excel(state.stage2_excel_path, "Responsible_Features", [log_entry])
                print(f"📋 Logged statement {current_statement_index + 1} analysis")

            print(f"\n✅ Identify Responsible Features completed for statement {current_statement_index + 1}")
            print(f"📊 responsible modules identified: {responsible_features}")

            # Clear validation feedback after use
            result = {
                "responsible_features": responsible_features
            }

            # Clear validation feedback if it was used
            if validation_feedback:
                result["validation_feedback"] = None
                print("🔄 Cleared validation feedback after use")

            return result

        except Exception as e:
            error_msg = f"❌ Identify Responsible Features failed: {str(e)}"
            print(error_msg)
            return {
                "responsible_features": [],  # Return empty list on error
                "error": error_msg
            }

    # ==================== HELPER METHODS ====================



    def load_keyword_mapping(self, migration_name: str, cloud_category: str) -> List[Dict[str, Any]]:
        """
        Load keyword-to-module mapping dynamically based on migration_name.

        Args:
            migration_name: Dynamic migration name from database configuration
            cloud_category: Cloud category from state (local/cloud)

        Returns:
            List of dictionaries containing Feature_Name, Keywords, Object_Path mapping
        """
        try:
            # Get QBook path dynamically based on cloud category from state
            if cloud_category.lower() == 'local':
                qbook_path = Config.Qbook_Local_Path
                print(f"🏠 Using Local QBook path: {qbook_path}")
            else:
                qbook_path = Config.Qbook_Path
                print(f"☁️ Using Cloud QBook path: {qbook_path}")

            # Dynamic CSV path: qbook_path/Conversion_Modules/{migration_name}/{migration_name}.csv
            csv_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, f"{migration_name}.csv")

            if not os.path.exists(csv_path):
                print(f"⚠️ {migration_name}.csv not found at {csv_path}")
                return []

            df = pd.read_csv(csv_path)
            keyword_mapping = df[['Feature_Name', 'Keywords', 'Object_Path']].to_dict('records')

            print(f"📋 Loaded {len(keyword_mapping)} keyword mappings from {csv_path}")
            return keyword_mapping

        except Exception as e:
            print(f"❌ Failed to load keyword mapping: {str(e)}")
            return []

    def get_module_paths(self, available_features: List[tuple], migration_name: str, cloud_category: str) -> List[tuple]:
        """
        Get full paths to Python modules from available_features using dynamic paths.

        Args:
            available_features: List of tuples like [('update_alias', 'Common/Statement/Pre/update_alias.py')]
            migration_name: Dynamic migration name from database configuration
            cloud_category: Cloud category from state (local/cloud)

        Returns:
            List of tuples with (feature_name, full_module_path)
        """
        module_paths = []

        # Get QBook path dynamically based on cloud category from state
        if cloud_category.lower() == 'local':
            qbook_path = Config.Qbook_Local_Path
            print(f"🏠 Using Local QBook path for modules: {qbook_path}")
        else:
            qbook_path = Config.Qbook_Path
            print(f"☁️ Using Cloud QBook path for modules: {qbook_path}")

        for feature_name, relative_path in available_features:
            # Dynamic module path: qbook_path/Conversion_Modules/{migration_name}/{relative_path}
            full_path = os.path.join(qbook_path, "Conversion_Modules", migration_name, relative_path)
            module_paths.append((feature_name, full_path))

        print(f"📁 Resolved {len(module_paths)} module paths for analysis")
        return module_paths

    def decrypt_and_read_modules(self, module_paths: List[tuple]) -> Dict[str, str]:
        """
        Decrypt and read Python modules from given paths using existing decryption logic.

        Args:
            module_paths: List of tuples with (feature_name, module_path)

        Returns:
            Dictionary mapping feature_name to decrypted module content
        """
        decrypted_modules = {}

        # Hardcoded decrypt key from object_conversion.py
        decrypt_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='

        for module_data in module_paths:
            try:
                # Handle different tuple formats safely
                if len(module_data) >= 2:
                    feature_name, module_path = module_data[0], module_data[1]
                else:
                    print(f"⚠️ Invalid module data format: {module_data}")
                    continue

                if os.path.exists(module_path):
                    # Use existing decryption function from object_conversion.py
                    decrypted_content = decrypt_conversion_file(module_path, decrypt_key)
                    decrypted_modules[feature_name] = decrypted_content
                    print(f"🔓 Decrypted module: {feature_name}")
                else:
                    print(f"⚠️ Module not found: {module_path}")

            except Exception as e:
                print(f"❌ Failed to decrypt module {feature_name}: {str(e)}")

        print(f"📚 Successfully decrypted {len(decrypted_modules)} modules")
        return decrypted_modules

    def ai_analyze_responsible_modules(self, statement_data: Dict[str, Any],
                                     decrypted_modules: Dict[str, str],
                                     keyword_mapping: List[Dict[str, Any]],
                                     migration_name: str,
                                     validation_feedback: Optional[str] = None) -> Dict[str, Any]:
        """
        Use AI to analyze which modules are responsible for conversion issues.

        Args:
            statement_data: Combined statement data with source, target, and AI converted statements
            decrypted_modules: Dictionary of decrypted Python module contents
            keyword_mapping: Keyword-to-module mapping from Oracle_Postgres14.csv
            migration_name: Migration name for database-specific terms
            validation_feedback: Optional feedback from previous validation attempts

        Returns:
            Dictionary containing responsible features analysis
        """
        try:
            # Extract key information for analysis
            original_source = statement_data.get('original_source_statement', '')
            ai_converted = statement_data.get('ai_converted_statement', '')
            actual_target = statement_data.get('original_target_statement', '')
            deployment_error = statement_data.get('original_deployment_error', '')
            available_features = statement_data.get('available_features', [])

            # Get database-specific terms for prompts (clean names without versions)
            db_terms = get_database_specific_terms(migration_name)

            # Prepare AI analysis prompt using prompts folder with validation feedback
            analysis_prompt = create_responsible_features_identification_prompt(
                original_source, ai_converted, actual_target,
                deployment_error, decrypted_modules, keyword_mapping, available_features,
                validation_feedback=validation_feedback,
                db_terms=db_terms
            )

            # Use structured output for reliable AI analysis
            structured_llm = self.llm.client.with_structured_output(ResponsibleFeaturesAnalysisOutput)
            ai_result = structured_llm.invoke(analysis_prompt)

            # Extract responsible features and analysis summary with complete module paths
            responsible_features = []
            for feature in ai_result.responsible_features:
                original_feature_name = feature.feature_name
                module_path = feature.module_path

                # Fix feature name to match actual Python function name (lowercase)
                # CSV might have "Xml_sequence" but function is "xml_sequence"
                corrected_feature_name = original_feature_name.lower()

                # Complete the module path if it's incomplete (from CSV keyword mapping)
                if module_path.endswith('.py'):
                    # Path is already complete (e.g., from available features)
                    complete_path = module_path
                else:
                    # Path is incomplete (e.g., from CSV keyword mapping like "Common/Pre")
                    # Add the lowercase feature name + .py (using corrected name)
                    complete_path = f"{module_path}/{corrected_feature_name}.py"

                # Get both AI responsibility reason and CSV keywords
                # AI responsibility reason: for responsibility tracking and context
                # CSV keywords: for enhancement prompts and module analysis
                ai_responsibility_reason = feature.responsibility_reason

                # Find CSV keywords for this feature from keyword mapping
                feature_keywords = ''
                for mapping in keyword_mapping:
                    if mapping.get('Feature_Name', '').lower() == corrected_feature_name.lower():
                        feature_keywords = mapping.get('Keywords', '')
                        break

                # Store feature with both AI responsibility reason and CSV keywords as tuple:
                # (feature_name, module_path, responsibility_reason, keywords)
                responsible_features.append((corrected_feature_name, complete_path, ai_responsibility_reason, feature_keywords))

                if original_feature_name != corrected_feature_name:
                    print(f"🔧 Corrected feature name: {original_feature_name} → {corrected_feature_name}")
                print(f"🔧 Completed path for {corrected_feature_name}: {complete_path}")
                if ai_responsibility_reason:
                    print(f"🎯 AI Responsibility for {corrected_feature_name}: {ai_responsibility_reason}")
                if feature_keywords:
                    print(f"🔑 CSV Keywords for {corrected_feature_name}: {feature_keywords}")

            analysis_summary = ai_result.analysis_summary

            print(f"🧠 AI identified {len(responsible_features)} responsible modules")

            if responsible_features:
                print("🎯 Responsible modules identified by AI:")
                for feature in ai_result.responsible_features:
                    print(f"   - {feature.feature_name}: {feature.responsibility_reason}")
            else:
                print("✅ No responsible modules identified - conversion working correctly")

            return {
                "responsible_features": responsible_features,
                "analysis_summary": analysis_summary
            }

        except Exception as e:
            print(f"❌ AI analysis failed: {str(e)}")
            return {
                "responsible_features": [],
                "analysis_summary": f"AI analysis failed: {str(e)}. Manual review required."
            }







    # ==================== MODULE UPDATE NODES ====================



















    # ==================== SEQUENTIAL DRIVER MODULE APPROACH METHODS ====================

    def extract_execution_context(self, current_statement_data: Dict[str, Any], responsible_features: List[tuple]) -> Dict[str, Any]:
        """Extract execution context including responsibility reasoning"""

        responsibility_context = self.extract_responsibility_context(responsible_features)

        # Debug: Log data sources for troubleshooting
        available_features = current_statement_data.get('available_features', [])
        post_features = current_statement_data.get('post_features', [])

        print(f"🔍 Data Source Debug:")
        print(f"   📊 Available features (Excel): {available_features}")
        print(f"   🎯 Responsible features (CSV): {responsible_features}")
        print(f"   📋 Post features (Excel): {post_features}")

        execution_context = {
            'input_statement': current_statement_data.get('statement_after_typecasting', ''),
            'expected_output': current_statement_data.get('ai_converted_statement', ''),
            'original_failed_output': current_statement_data.get('original_target_statement', ''),
            'deployment_error': current_statement_data.get('original_deployment_error', ''),
            'available_modules': available_features,  # FROM EXCEL - Primary source
            'responsible_modules': responsible_features,  # FROM CSV - Secondary source
            'post_execution_modules': post_features,  # FROM EXCEL - Primary source
            'responsibility_context': responsibility_context,
            'responsible_modules_with_reasoning': responsibility_context['responsible_modules_with_reasoning'],
            'responsibility_summary': responsibility_context['responsibility_summary'],
            'statement_number': current_statement_data.get('source_statement_number', 0),
            'original_source': current_statement_data.get('original_source_statement', ''),
            'original_target': current_statement_data.get('original_target_statement', '')
        }

        print(f"📊 Execution Context: {len(responsibility_context['responsible_modules_with_reasoning'])} responsible modules")
        print(f"📊 Responsibility Summary: {responsibility_context['responsibility_summary']}")

        return execution_context

    def extract_responsibility_context(self, responsible_features: List[tuple]) -> Dict[str, Any]:
        """Extract responsibility reasoning from identification node output"""

        print(f"🔍 DEBUG: Extracting responsibility context from {len(responsible_features)} features")
        for i, feature_tuple in enumerate(responsible_features):
            print(f"   Feature {i}: {feature_tuple} (length: {len(feature_tuple)})")

        responsibility_context = {
            'responsible_modules_with_reasoning': [],
            'responsibility_summary': '',
            'total_responsible_modules': len(responsible_features)
        }

        for feature_tuple in responsible_features:
            module_info = {
                'module_name': feature_tuple[0] if len(feature_tuple) > 0 else 'unknown',
                'module_path': feature_tuple[1] if len(feature_tuple) > 1 else '',
                'responsibility_reason': feature_tuple[2] if len(feature_tuple) > 2 else 'No specific reasoning provided',
                'keywords': feature_tuple[3] if len(feature_tuple) > 3 else ''
            }

            print(f"   📋 Created module_info: {module_info}")
            responsibility_context['responsible_modules_with_reasoning'].append(module_info)

        responsibility_context['responsibility_summary'] = self.create_responsibility_summary(
            responsibility_context['responsible_modules_with_reasoning']
        )

        return responsibility_context

    def create_responsibility_summary(self, modules_with_reasoning: List[Dict[str, Any]]) -> str:
        """Create summary of all responsibility reasons"""
        summary_parts = []
        for module_info in modules_with_reasoning:
            summary_parts.append(f"{module_info['module_name']}: {module_info['responsibility_reason']}")

        return "; ".join(summary_parts)

    def categorize_modules_by_execution_sequence(self, execution_context: Dict[str, Any]) -> Dict[str, List[tuple]]:
        """Split modules into pre-execution, responsible, and post-execution phases using Excel as single source"""

        available_modules = execution_context['available_modules']  # FROM EXCEL
        responsible_modules = execution_context['responsible_modules']  # FROM CSV
        post_execution_modules = execution_context['post_execution_modules']  # FROM EXCEL

        print(f"🔍 Categorization Debug:")
        print(f"   📊 Available modules (Excel): {available_modules}")
        print(f"   🎯 Responsible modules (CSV): {responsible_modules}")
        print(f"   📋 Post modules (Excel): {post_execution_modules}")

        # SOLUTION: Use Excel available_modules as the primary source
        # Find responsible modules within available_modules by name matching
        responsible_names = set()
        for responsible_tuple in responsible_modules:
            if len(responsible_tuple) > 0:
                responsible_names.add(responsible_tuple[0])

        print(f"   🔍 Looking for responsible names: {responsible_names}")

        # Find positions of responsible modules within available_modules (Excel source)
        responsible_positions = []
        excel_responsible_modules = []
        csv_only_responsible_modules = []

        # Step 1: Find responsible modules that exist in Excel
        for i, available_tuple in enumerate(available_modules):
            if len(available_tuple) > 0 and available_tuple[0] in responsible_names:
                responsible_positions.append(i)
                excel_responsible_modules.append(available_tuple)
                print(f"   ✅ Found responsible module in Excel: {available_tuple} at position {i}")

        # Step 2: Find responsible modules that only exist in CSV (not in Excel)
        excel_responsible_names = {module[0] for module in excel_responsible_modules}
        for responsible_tuple in responsible_modules:
            if len(responsible_tuple) > 0 and responsible_tuple[0] not in excel_responsible_names:
                csv_only_responsible_modules.append(responsible_tuple)
                print(f"   📋 Responsible module only in CSV: {responsible_tuple}")

        # Step 3: Combine Excel and CSV responsible modules
        all_responsible_modules = excel_responsible_modules + csv_only_responsible_modules

        module_categories = {
            'pre_execution_modules': [],
            'responsible_modules': all_responsible_modules,  # Combined Excel + CSV
            'post_execution_modules': post_execution_modules
        }

        # Categorize remaining available_modules as pre-execution
        if responsible_positions:
            first_responsible_pos = min(responsible_positions)
            print(f"   📍 First responsible position in Excel: {first_responsible_pos}")

            # Add modules before first responsible position as pre-execution
            for i, available_tuple in enumerate(available_modules):
                if i < first_responsible_pos:
                    module_categories['pre_execution_modules'].append(available_tuple)
                    print(f"   🔄 Added to pre-execution: {available_tuple}")
        else:
            # If no responsible modules found in Excel, all available modules are pre-execution
            print(f"   ⚠️ No responsible modules found in Excel")
            if excel_responsible_modules:
                # This shouldn't happen, but handle gracefully
                module_categories['pre_execution_modules'] = available_modules
            else:
                # All available modules become pre-execution since responsible are CSV-only
                module_categories['pre_execution_modules'] = available_modules
                print(f"   🔄 All Excel modules added to pre-execution: {available_modules}")

        print(f"   📊 Final categorization:")
        print(f"      🔄 Pre-execution: {len(module_categories['pre_execution_modules'])} modules")
        print(f"      🎯 Responsible: {len(module_categories['responsible_modules'])} modules")
        print(f"      📋 Post-execution: {len(module_categories['post_execution_modules'])} modules")

        self.log_categorization(module_categories)
        return module_categories

    def log_categorization(self, module_categories: Dict[str, List[tuple]]) -> None:
        """Log module categorization results"""
        print(f"📋 Module Categories:")
        print(f"   🔄 Pre-execution: {self.extract_module_names(module_categories['pre_execution_modules'])}")
        print(f"   🎯 Responsible: {self.extract_module_names(module_categories['responsible_modules'])}")
        print(f"   📋 Post-execution: {self.extract_module_names(module_categories['post_execution_modules'])}")

    def extract_module_names(self, module_tuples: List[tuple]) -> List[str]:
        """Extract module names from tuple list"""
        names = []
        for module_tuple in module_tuples:
            if len(module_tuple) > 0:
                names.append(module_tuple[0])
        return names

    def execute_enhanced_sequential_workflow(self, execution_context: Dict[str, Any], module_categories: Dict[str, List[tuple]],
                                           ai_comparison_feedback: str, state: Stage2WorkflowState,
                                           paths_info: Dict[str, str], attempt_number: int, max_iterations: int = 3) -> Dict[str, Any]:
        """Execute complete enhanced sequential workflow with iterative testing"""
        try:
            pre_execution_result = self.execute_pre_execution_phase(
                execution_context['input_statement'], module_categories['pre_execution_modules'], state
            )

            enhancement_context = self.build_enhancement_context(
                execution_context, pre_execution_result, module_categories, ai_comparison_feedback, state
            )

            print("🔄 Starting iterative enhancement and testing...")
            last_enhancement_result = None

            for iteration in range(max_iterations):
                iteration_number = iteration + 1
                print(f"🔄 Enhancement iteration {iteration_number}/{max_iterations}")

                enhancement_result = self.enhance_responsible_modules_phase(
                    module_categories['responsible_modules'], enhancement_context, state
                )

                # Check if enhancement succeeded
                if not enhancement_result['success']:
                    print(f"❌ Enhancement iteration {iteration_number} failed completely - no modules to save")
                    continue

                # Store last enhancement result for fallback
                last_enhancement_result = enhancement_result

                # ALWAYS save iteration modules for verification (guaranteed to have modules now)
                self.save_iteration_modules_for_verification(
                    enhancement_result['enhanced_modules'], paths_info, iteration_number, state
                )

                testing_result = self.test_complete_flow_phase(
                    enhancement_context, enhancement_result['enhanced_modules'],
                    module_categories['post_execution_modules'], state
                )

                if testing_result['success']:
                    print(f"✅ Sequential workflow successful after {iteration_number} iterations")

                    # Save final successful modules with current_attempt basis
                    saved_modules = self.save_final_successful_modules(
                        enhancement_result['enhanced_modules'], paths_info, attempt_number, iteration_number, state
                    )

                    self.log_sequential_enhancement_to_excel(
                        state.stage2_excel_path, enhancement_result, testing_result,
                        module_categories, attempt_number, iteration_number, execution_context['responsibility_context']
                    )

                    return {
                        'success': True,
                        'enhanced_modules': saved_modules,
                        'iterations': iteration_number,
                        'testing_result': testing_result,
                        'pre_execution_result': pre_execution_result
                    }

                iteration_feedback = self.generate_iteration_feedback(testing_result, enhancement_context)
                enhancement_context['iteration_feedback'] = iteration_feedback
                print(f"❌ Iteration {iteration_number} testing failed, generating feedback for retry...")

            # All attempts failed - create final attempt file from last iteration
            print(f"❌ All {max_iterations} iterations failed. Creating final attempt file as attempt_{attempt_number}...")

            # We're guaranteed to have enhanced_modules from last iteration
            final_modules = self.save_final_attempt_modules(
                last_enhancement_result['enhanced_modules'], paths_info, attempt_number, max_iterations, state
            )

            return {
                'success': False,
                'error': f'Sequential workflow failed after {max_iterations} iterations - saved final attempt as attempt_{attempt_number}',
                'enhanced_modules': final_modules,
                'final_attempt_created': True
            }

            return {
                'success': False,
                'error': f'Sequential workflow failed after {max_iterations} iterations',
                'enhanced_modules': []
            }

        except Exception as e:
            print(f"❌ Enhanced sequential workflow error: {str(e)}")
            return {'success': False, 'error': str(e), 'enhanced_modules': []}

    def execute_pre_execution_phase(self, input_statement: str, pre_execution_modules: List[tuple], state: Stage2WorkflowState) -> Dict[str, Any]:
        """Execute pre-execution modules to get actual input for responsible modules"""
        try:
            current_output = input_statement
            execution_log = []

            print(f"🔄 Executing {len(pre_execution_modules)} pre-execution modules...")

            for module_tuple in pre_execution_modules:
                if len(module_tuple) >= 2:
                    module_name, module_path = module_tuple[0], module_tuple[1]
                    module_code = self.read_and_decrypt_module(module_name, module_path, state.migration_name, state.cloud_category)

                    if module_code:
                        previous_output = current_output
                        print(f"🔍 PRE MODULE INPUT for {module_name}:")
                        print(f"📝 Input: {previous_output}")

                        current_output = self.apply_feature_module(current_output, module_name, module_code, state)

                        print(f"📤 PRE MODULE OUTPUT for {module_name}:")
                        print(f"📝 Output: {current_output}")

                        execution_log.append({
                            'module_name': module_name,
                            'module_path': module_path,
                            'input_length': len(previous_output),
                            'output_length': len(current_output),
                            'transformation_applied': current_output != previous_output
                        })

                        print(f"   ✅ {module_name}: {len(previous_output)} → {len(current_output)} chars")

            print(f"📤 Pre-execution output: {len(current_output)} chars")

            return {
                'success': True,
                'output': current_output,
                'execution_log': execution_log,
                'modules_executed': len(pre_execution_modules)
            }

        except Exception as e:
            print(f"❌ Pre-execution phase error: {str(e)}")
            return {'success': False, 'output': input_statement, 'execution_log': [], 'error': str(e)}

    def build_enhancement_context(self, execution_context: Dict[str, Any], pre_execution_result: Dict[str, Any],
                                module_categories: Dict[str, List[tuple]], ai_comparison_feedback: str,
                                state: Stage2WorkflowState) -> Dict[str, Any]:
        """Build comprehensive enhancement context WITHOUT executing original modules"""

        # DO NOT execute original modules - just use the failed output for context
        current_responsible_output = execution_context['original_failed_output']  # Use failed output instead of executing
        current_complete_output = execution_context['original_failed_output']     # Use failed output instead of executing

        enhancement_context = {
            'actual_responsible_input': pre_execution_result['output'],
            'expected_final_output': execution_context['expected_output'],
            'original_input_statement': execution_context['input_statement'],
            'original_failed_output': execution_context['original_failed_output'],
            'current_responsible_output': current_responsible_output,
            'current_complete_output': current_complete_output,
            'ai_corrected_output': execution_context['expected_output'],
            'responsibility_summary': execution_context['responsibility_summary'],
            'responsible_modules_with_reasoning': execution_context['responsible_modules_with_reasoning'],
            'responsibility_context': execution_context['responsibility_context'],
            'pre_execution_log': pre_execution_result['execution_log'],
            'pre_execution_modules': self.extract_module_names_from_log(pre_execution_result['execution_log']),
            'deployment_error': execution_context['deployment_error'],
            'original_source_statement': execution_context['original_source'],
            'original_target_statement': execution_context['original_target'],
            'ai_comparison_feedback': ai_comparison_feedback,
            'iteration_feedback': None,
            'migration_name': state.migration_name,
            'db_terms': get_database_specific_terms(state.migration_name),
            'statement_number': execution_context['statement_number']
        }

        print(f"📋 Enhancement Context Built (NO original module execution):")
        print(f"   📝 Actual responsible input: {len(enhancement_context['actual_responsible_input'])} chars")
        print(f"   🔧 Using original failed output: {len(current_responsible_output)} chars")
        print(f"   🎯 Expected final output: {len(enhancement_context['expected_final_output'])} chars")

        return enhancement_context



    def extract_module_names_from_log(self, execution_log: List[Dict[str, Any]]) -> List[str]:
        """Extract module names from execution log"""
        names = []
        for log_entry in execution_log:
            if 'module_name' in log_entry:
                names.append(log_entry['module_name'])
        return names

    def enhance_responsible_modules_phase(self, responsible_modules: List[tuple], enhancement_context: Dict[str, Any],
                                        state: Stage2WorkflowState) -> Dict[str, Any]:
        """Enhance responsible modules using driver approach with sequential context"""
        try:
            responsible_modules_with_code = []
            for module_tuple in responsible_modules:
                if len(module_tuple) >= 2:
                    module_name, module_path = module_tuple[0], module_tuple[1]
                    module_code = self.read_and_decrypt_module(module_name, module_path, state.migration_name, state.cloud_category)
                    if module_code:
                        responsible_modules_with_code.append((module_name, module_path, module_code))

            if not responsible_modules_with_code:
                return {'success': False, 'error': 'No responsible modules could be read', 'enhanced_modules': []}

            driver_enhancer = DriverModuleEnhancer(self.llm.client)

            combined_code, module_boundaries = driver_enhancer.combine_responsible_modules(responsible_modules_with_code)

            enhanced_code = self.ai_enhance_with_sequential_context(combined_code, enhancement_context)

            original_modules = {name: code for name, _, code in responsible_modules_with_code}
            decomposition_results = driver_enhancer.decompose_and_detect_changes(
                original_modules, enhanced_code, module_boundaries
            )

            enhanced_modules = []
            for module_name, result in decomposition_results.items():
                if result['action'] == 'create_attempt':
                    enhanced_modules.append({
                        'statement_number': enhancement_context['statement_number'],  # Add statement number
                        'module_name': module_name,
                        'module_path': result['module_path'],
                        'enhanced_code': result['enhanced_code'],
                        'code_changed': True,
                        'enhancement_type': 'sequential_driver_approach'
                    })

            # If no modules were enhanced, create modules anyway for iteration files
            if len(enhanced_modules) == 0:
                print(f"⚠️ No functional changes detected - creating modules anyway for iteration files")
                for module_name, original_code in original_modules.items():
                    if module_name in module_boundaries:
                        # Extract the enhanced code even if no changes detected
                        enhanced_module_code = driver_enhancer.extract_module_from_combined(
                            enhanced_code, module_boundaries[module_name]
                        )
                        if enhanced_module_code:
                            enhanced_modules.append({
                                'statement_number': enhancement_context['statement_number'],
                                'module_name': module_name,
                                'module_path': module_boundaries[module_name]['module_path'],
                                'enhanced_code': enhanced_module_code,
                                'code_changed': False,  # Mark as no changes but still create files
                                'enhancement_type': 'sequential_driver_no_changes'
                            })
                        else:
                            # If extraction fails, use original code
                            enhanced_modules.append({
                                'statement_number': enhancement_context['statement_number'],
                                'module_name': module_name,
                                'module_path': module_boundaries[module_name]['module_path'],
                                'enhanced_code': original_code,
                                'code_changed': False,
                                'enhancement_type': 'sequential_driver_original'
                            })

            print(f"🔧 Created {len(enhanced_modules)} modules for iteration files")

            # If no modules were enhanced, create fallback modules for debugging
            if len(enhanced_modules) == 0:
                print(f"⚠️ No functional changes detected - creating fallback modules for debugging")
                for module_name, original_code in original_modules.items():
                    if module_name in module_boundaries:
                        # Extract the enhanced code even if no changes detected
                        enhanced_module_code = driver_enhancer.extract_module_from_combined(
                            enhanced_code, module_boundaries[module_name]
                        )
                        if enhanced_module_code:
                            enhanced_modules.append({
                                'statement_number': enhancement_context['statement_number'],
                                'module_name': module_name,
                                'module_path': module_boundaries[module_name]['module_path'],
                                'enhanced_code': enhanced_module_code,
                                'code_changed': False,  # Mark as no changes for debugging
                                'enhancement_type': 'sequential_driver_fallback'
                            })
                print(f"🔧 Created {len(enhanced_modules)} fallback modules for debugging")

            # GUARANTEE: enhanced_modules is NEVER empty - always return modules for file creation
            if len(enhanced_modules) == 0:
                print(f"⚠️ No enhanced modules created - using original modules for iteration files...")
                # Use original modules when no enhancements are detected
                for module_name, original_code in original_modules.items():
                    if module_name in module_boundaries:
                        enhanced_modules.append({
                            'statement_number': enhancement_context['statement_number'],
                            'module_name': module_name,
                            'module_path': module_boundaries[module_name]['module_path'],
                            'enhanced_code': original_code,  # Use original code
                            'code_changed': False,
                            'enhancement_type': 'sequential_original_module'
                        })
                print(f"✅ Created {len(enhanced_modules)} modules from original code")

            return {
                'success': True,
                'enhanced_modules': enhanced_modules,
                'decomposition_results': decomposition_results,
                'combined_code': combined_code,
                'enhanced_combined_code': enhanced_code
            }

        except Exception as e:
            print(f"❌ Responsible module enhancement error: {str(e)}")
            return {'success': False, 'error': str(e), 'enhanced_modules': []}

    def ai_enhance_with_sequential_context(self, combined_code: str, enhancement_context: Dict[str, Any]) -> str:
        """Use AI to enhance combined module with sequential execution context and responsibility reasoning"""
        try:
            prompt = create_sequential_enhancement_prompt(combined_code, enhancement_context)

            structured_llm = self.llm.client.with_structured_output(ModuleEnhancementOutput)
            ai_result = structured_llm.invoke(prompt)

            print(f"🤖 AI enhancement completed with sequential context")
            print(f"   🔧 AI reported code_changed: {ai_result.code_changed}")
            print(f"   📝 AI analysis: {ai_result.analysis[:150]}...")
            print(f"   📊 Original code length: {len(combined_code)} chars")
            print(f"   📊 Enhanced code length: {len(ai_result.enhanced_code)} chars")

            # Check if AI actually made changes
            if combined_code.strip() == ai_result.enhanced_code.strip():
                print(f"   ⚠️ WARNING: AI returned identical code (no actual changes)")
            else:
                print(f"   ✅ AI made actual changes to the code")

            return ai_result.enhanced_code

        except Exception as e:
            print(f"❌ AI enhancement error: {str(e)}")
            return combined_code





    def test_complete_flow_phase(self, enhancement_context: Dict[str, Any], enhanced_modules: List[Dict[str, Any]],
                               post_execution_modules: List[tuple], state: Stage2WorkflowState) -> Dict[str, Any]:
        """Test complete flow: actual_responsible_input → enhanced_responsible → post_execution → final_output"""
        try:
            current_output = enhancement_context['actual_responsible_input']
            responsible_execution_log = []

            print(f"🔄 Testing enhanced responsible modules...")
            for enhanced_module in enhanced_modules:
                module_name = enhanced_module['module_name']
                enhanced_code = enhanced_module['enhanced_code']

                module_responsibility = self.get_module_responsibility(
                    module_name, enhancement_context['responsible_modules_with_reasoning']
                )

                previous_output = current_output
                print(f"🔍 ENHANCED MODULE INPUT for {module_name}:")
                print(f"📝 Input: {previous_output}")

                current_output = self.apply_feature_module(current_output, module_name, enhanced_code, state)

                print(f"📤 ENHANCED MODULE OUTPUT for {module_name}:")
                print(f"📝 Output: {current_output}")

                # Debug same content issue
                if previous_output == current_output:
                    print(f"⚠️ Module {module_name} produced same output as input - no transformation applied")
                    print(f"   📝 Input preview: {previous_output[:100]}...")
                    print(f"   🔧 Enhanced code preview: {enhanced_code[:200]}...")
                else:
                    print(f"✅ Module {module_name} successfully transformed the input")

                transformation_validation = self.validate_transformation(
                    module_name, module_responsibility, previous_output, current_output
                )

                responsible_execution_log.append({
                    'module_name': module_name,
                    'input_length': len(previous_output),
                    'output_length': len(current_output),
                    'transformation_applied': current_output != previous_output,
                    'responsibility_reason': module_responsibility.get('responsibility_reason', ''),
                    'transformation_validated': transformation_validation['validated'],
                    'transformation_analysis': transformation_validation['analysis']
                })

                print(f"   🔧 Enhanced {module_name}: {len(previous_output)} → {len(current_output)} chars")
                print(f"      📋 Responsibility: {module_responsibility.get('responsibility_reason', 'Unknown')}")

                validation_icon = "✅" if transformation_validation['validated'] else "❌"
                print(f"      {validation_icon} Validated: {transformation_validation['validated']}")

            responsible_output = current_output

            post_execution_log = []
            print(f"🔄 Testing post-execution modules...")
            for module_tuple in post_execution_modules:
                if len(module_tuple) >= 2:
                    module_name, module_path = module_tuple[0], module_tuple[1]
                    module_code = self.read_and_decrypt_module(module_name, module_path, state.migration_name, state.cloud_category)

                    if module_code:
                        previous_output = current_output
                        print(f"🔍 POST MODULE INPUT for {module_name}:")
                        print(f"📝 Input: {previous_output}")

                        current_output = self.apply_feature_module(current_output, module_name, module_code, state)

                        print(f"📤 POST MODULE OUTPUT for {module_name}:")
                        print(f"📝 Output: {current_output}")

                        post_execution_log.append({
                            'module_name': module_name,
                            'input_length': len(previous_output),
                            'output_length': len(current_output),
                            'transformation_applied': current_output != previous_output
                        })

                        print(f"   📋 Post {module_name}: {len(previous_output)} → {len(current_output)} chars")

            final_output = current_output

            comparison_result = self.ai_compare_statement_functionality(
                statement_data={
                    'ai_converted_statement': enhancement_context['expected_final_output'],
                    'updated_ai_converted_statement': final_output
                },
                migration_name=state.migration_name
            )

            testing_result = {
                'success': comparison_result['statements_match'],
                'final_output': final_output,
                'expected_output': enhancement_context['expected_final_output'],
                'comparison_analysis': comparison_result['explanation'],
                'execution_trace': {
                    'responsible_input': enhancement_context['actual_responsible_input'],
                    'responsible_output': responsible_output,
                    'final_output': final_output
                },
                'execution_logs': {
                    'responsible_execution': responsible_execution_log,
                    'post_execution': post_execution_log
                },
                'transformation_validation': {
                    'all_transformations_validated': all(log['transformation_validated'] for log in responsible_execution_log),
                    'transformation_details': responsible_execution_log
                }
            }

            print(f"📊 Complete flow test: {'SUCCESS' if testing_result['success'] else 'FAILED'}")
            print(f"📋 Transformation validation: {'ALL VALIDATED' if testing_result['transformation_validation']['all_transformations_validated'] else 'SOME FAILED'}")

            return testing_result

        except Exception as e:
            print(f"❌ Complete flow testing error: {str(e)}")
            return {'success': False, 'error': str(e)}

    def get_module_responsibility(self, module_name: str, responsible_modules_with_reasoning: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Get responsibility information for specific module"""
        print(f"🔍 Looking for responsibility for module: {module_name}")
        print(f"🔍 Available modules: {[m.get('module_name') for m in responsible_modules_with_reasoning]}")

        for module_info in responsible_modules_with_reasoning:
            if module_info.get('module_name') == module_name:
                print(f"✅ Found responsibility: {module_info.get('responsibility_reason', 'Unknown')}")
                return module_info

        print(f"❌ No responsibility found for module: {module_name}")
        return {'responsibility_reason': 'No responsibility information found'}

    def validate_transformation(self, module_name: str, module_responsibility: Dict[str, Any],
                              input_statement: str, output_statement: str) -> Dict[str, bool]:
        """Validate that module applied transformation based on responsibility"""

        if input_statement != output_statement:
            return {
                'validated': True,
                'analysis': f'Module {module_name} applied transformation based on responsibility: {module_responsibility.get("responsibility_reason", "Unknown")}'
            }
        else:
            return {
                'validated': False,
                'analysis': f'Module {module_name} did not apply any transformation'
            }



    def save_iteration_modules_for_verification(self, enhanced_modules: List[Dict[str, Any]], paths_info: Dict[str, str],
                                              iteration_number: int, state: Stage2WorkflowState) -> None:
        """Save iteration modules with sequential_iter naming for verification"""
        try:
            print(f"💾 Saving iteration {iteration_number} modules for verification...")

            for enhanced_module in enhanced_modules:
                module_name = enhanced_module['module_name']
                enhanced_code = enhanced_module['enhanced_code']

                # Save with sequential iteration naming for verification
                iter_filename = f"{module_name}_sequential_iter_{iteration_number}.py"
                iter_filepath = os.path.join(paths_info['feature_modules_dir'], iter_filename)

                print(f"📁 Creating iteration file: {iter_filepath}")
                os.makedirs(os.path.dirname(iter_filepath), exist_ok=True)

                with open(iter_filepath, 'w', encoding='utf-8') as f:
                    f.write(enhanced_code)

                print(f"💾 Saved iteration module: {module_name}_sequential_iter_{iteration_number}.py")

        except Exception as e:
            print(f"❌ Error saving iteration modules: {str(e)}")

    def save_iteration_modules_from_original(self, responsible_modules: List[tuple], paths_info: Dict[str, str],
                                           iteration_number: int, state: Stage2WorkflowState) -> None:
        """Save iteration modules from original code when AI makes no changes"""
        try:
            print(f"💾 Saving iteration {iteration_number} modules from original code (AI made no changes)...")

            for module_tuple in responsible_modules:
                if len(module_tuple) >= 2:
                    module_name, module_path = module_tuple[0], module_tuple[1]

                    # Read original module code
                    original_code = self.read_and_decrypt_module(module_name, module_path, state.migration_name, state.cloud_category)

                    if original_code:
                        # Save with sequential iteration naming
                        iter_filename = f"{module_name}_sequential_iter_{iteration_number}.py"
                        iter_filepath = os.path.join(paths_info['feature_modules_dir'], iter_filename)

                        print(f"📁 Creating iteration file from original: {iter_filepath}")
                        os.makedirs(os.path.dirname(iter_filepath), exist_ok=True)

                        with open(iter_filepath, 'w', encoding='utf-8') as f:
                            f.write(original_code)

                        print(f"💾 Saved original module as iteration: {module_name}_sequential_iter_{iteration_number}.py")

        except Exception as e:
            print(f"❌ Error saving iteration modules from original: {str(e)}")

    def save_final_successful_modules(self, enhanced_modules: List[Dict[str, Any]], paths_info: Dict[str, str],
                                    attempt_number: int, successful_iteration: int, state: Stage2WorkflowState) -> List[Dict[str, Any]]:
        """Save final successful modules with current_attempt basis"""
        try:
            final_modules = []

            print(f"💾 Saving final successful modules from iteration {successful_iteration} as attempt_{attempt_number}...")

            for enhanced_module in enhanced_modules:
                module_name = enhanced_module['module_name']
                enhanced_code = enhanced_module['enhanced_code']

                # Save final successful module with current attempt number
                final_filename = f"{module_name}_attempt_{attempt_number}.py"
                final_filepath = os.path.join(paths_info['feature_modules_dir'], final_filename)

                print(f"📁 Creating file: {final_filepath}")
                os.makedirs(os.path.dirname(final_filepath), exist_ok=True)

                with open(final_filepath, 'w', encoding='utf-8') as f:
                    f.write(enhanced_code)

                final_module = {
                    'statement_number': enhanced_module.get('statement_number', 0),
                    'feature_name': module_name,
                    'module_path': enhanced_module['module_path'],
                    'updated_module_path': final_filepath,
                    'original_code': '',
                    'updated_code': enhanced_code,
                    'enhancement_analysis': f'Sequential driver approach - successful iteration {successful_iteration}',
                    'code_changed': enhanced_module['code_changed'],
                    'attempt_number': attempt_number,
                    'enhancement_type': 'sequential_successful'
                }

                final_modules.append(final_module)
                self.log_module_update_to_excel(state.stage2_excel_path, final_module, "SUCCESS")

                print(f"💾 Saved final successful module: {module_name}_attempt_{attempt_number}.py")

            return final_modules

        except Exception as e:
            print(f"❌ Error saving final successful modules: {str(e)}")
            return []

    def save_final_attempt_modules(self, enhanced_modules: List[Dict[str, Any]], paths_info: Dict[str, str],
                                 attempt_number: int, last_iteration: int, state: Stage2WorkflowState) -> List[Dict[str, Any]]:
        """Save final attempt modules from last iteration with current_attempt basis"""
        try:
            final_modules = []

            print(f"💾 Saving final attempt modules from iteration {last_iteration} as attempt_{attempt_number}...")

            for enhanced_module in enhanced_modules:
                module_name = enhanced_module['module_name']
                enhanced_code = enhanced_module['enhanced_code']

                # Save final attempt module with current attempt number
                attempt_filename = f"{module_name}_attempt_{attempt_number}.py"
                attempt_filepath = os.path.join(paths_info['feature_modules_dir'], attempt_filename)

                print(f"📁 Creating final attempt file: {attempt_filepath}")
                os.makedirs(os.path.dirname(attempt_filepath), exist_ok=True)

                with open(attempt_filepath, 'w', encoding='utf-8') as f:
                    f.write(enhanced_code)

                final_module = {
                    'statement_number': enhanced_module.get('statement_number', 0),
                    'feature_name': module_name,
                    'module_path': enhanced_module['module_path'],
                    'updated_module_path': attempt_filepath,
                    'original_code': '',
                    'updated_code': enhanced_code,
                    'enhancement_analysis': f'Sequential driver approach - final attempt from iteration {last_iteration}',
                    'code_changed': enhanced_module['code_changed'],
                    'attempt_number': attempt_number,
                    'enhancement_type': 'sequential_final_attempt'
                }

                final_modules.append(final_module)
                self.log_module_update_to_excel(state.stage2_excel_path, final_module, "FINAL_ATTEMPT")

                print(f"💾 Saved final attempt module: {module_name}_attempt_{attempt_number}.py")

            return final_modules

        except Exception as e:
            print(f"❌ Error saving final attempt modules: {str(e)}")
            return []



    def generate_iteration_feedback(self, testing_result: Dict[str, Any], enhancement_context: Dict[str, Any]) -> str:
        """Generate feedback for next enhancement iteration using prompts folder"""
        return create_iteration_feedback_prompt(testing_result, enhancement_context)

    def save_final_attempt_from_original(self, responsible_modules: List[tuple], paths_info: Dict[str, str],
                                       attempt_number: int, state: Stage2WorkflowState) -> List[Dict[str, Any]]:
        """Save final attempt modules from original code when no enhanced modules available"""
        try:
            final_modules = []

            print(f"💾 Saving final attempt from original modules as attempt_{attempt_number}...")

            for module_tuple in responsible_modules:
                if len(module_tuple) >= 2:
                    module_name, module_path = module_tuple[0], module_tuple[1]

                    # Read original module code
                    original_code = self.read_and_decrypt_module(module_name, module_path, state.migration_name, state.cloud_category)

                    if original_code:
                        # Save final attempt module with current attempt number
                        attempt_filename = f"{module_name}_attempt_{attempt_number}.py"
                        attempt_filepath = os.path.join(paths_info['feature_modules_dir'], attempt_filename)

                        print(f"📁 Creating final attempt file from original: {attempt_filepath}")
                        os.makedirs(os.path.dirname(attempt_filepath), exist_ok=True)

                        with open(attempt_filepath, 'w', encoding='utf-8') as f:
                            f.write(original_code)

                        final_module = {
                            'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                            'feature_name': module_name,
                            'module_path': module_path,
                            'updated_module_path': attempt_filepath,
                            'original_code': original_code,
                            'updated_code': original_code,
                            'enhancement_analysis': 'Sequential driver approach - final attempt from original (no AI changes)',
                            'code_changed': False,
                            'attempt_number': attempt_number,
                            'enhancement_type': 'sequential_original_attempt'
                        }

                        final_modules.append(final_module)
                        self.log_module_update_to_excel(state.stage2_excel_path, final_module, "ORIGINAL_ATTEMPT")

                        print(f"💾 Saved original attempt module: {module_name}_attempt_{attempt_number}.py")

            return final_modules

        except Exception as e:
            print(f"❌ Error saving original attempt modules: {str(e)}")
            return []

    def log_sequential_enhancement_to_excel(self, excel_path: str, enhancement_result: Dict[str, Any],
                                          testing_result: Dict[str, Any], module_categories: Dict[str, List[tuple]],
                                          attempt_number: int, iterations: int, responsibility_context: Dict[str, Any]) -> None:
        """Log sequential enhancement with responsibility details using same pattern as other nodes"""
        try:
            sequential_data = {
                'Statement_Number': enhancement_result.get('statement_number', 0),
                'Attempt_Number': attempt_number,
                'Enhancement_Type': 'SEQUENTIAL_DRIVER_MODULE',
                'Pre_Execution_Modules': ', '.join(self.extract_module_names(module_categories['pre_execution_modules'])),
                'Responsible_Modules': ', '.join(self.extract_module_names(module_categories['responsible_modules'])),
                'Post_Execution_Modules': ', '.join(self.extract_module_names(module_categories['post_execution_modules'])),
                'Responsibility_Summary': responsibility_context['responsibility_summary'],
                'Enhancement_Iterations': iterations,
                'Complete_Flow_Test_Success': testing_result.get('success', False),
                'All_Transformations_Validated': testing_result.get('transformation_validation', {}).get('all_transformations_validated', False),
                'Enhanced_Modules_Count': len(enhancement_result.get('enhanced_modules', [])),
                'Final_Output_Length': len(testing_result.get('final_output', '')),
                'Expected_Output_Length': len(testing_result.get('expected_output', '')),
                'Comparison_Analysis': testing_result.get('comparison_analysis', ''),
                'Enhancement_Status': 'SUCCESS' if enhancement_result.get('success', False) else 'FAILED',
                'Timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'Analysis': f"Sequential enhancement: {iterations} iterations, {len(enhancement_result.get('enhanced_modules', []))} modules enhanced, complete flow test {'passed' if testing_result.get('success', False) else 'failed'}"
            }

            # Use existing append_sheet_to_excel function (same pattern as other nodes)
            append_sheet_to_excel(excel_path, "Sequential_Enhancement", [sequential_data])
            print(f"📊 Logged sequential enhancement to Excel")

        except Exception as e:
            print(f"❌ Error logging sequential enhancement: {str(e)}")

    def setup_module_update_paths(self, state: Stage2WorkflowState, statement_number: int, attempt_number: int) -> Dict[str, str]:
        """
        Setup paths for Stage1_Metadata directory structure with statement-specific directories and attempt tracking.

        Args:
            state: Stage2WorkflowState containing migration details
            statement_number: Current statement number (1-based) for directory organization
            attempt_number: Current attempt number for tracking

        Returns:
            Dictionary containing path information
        """
        try:
            # Get cloud category from state
            cloud_category = getattr(state, 'cloud_category', 'cloud')

            # Get Stage1_Metadata path based on cloud category
            if cloud_category.lower() == 'local':
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get dynamic folder name based on process type
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            # Create directory structure: Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/{process_type}_feature_modules/{statement_number}
            feature_modules_base_dir = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            # Statement-specific directory
            feature_modules_dir = os.path.join(feature_modules_base_dir, str(statement_number))

            # Create directory if it doesn't exist
            os.makedirs(feature_modules_dir, exist_ok=True)

            print(f"📁 Feature modules directory ({feature_modules_folder}): {feature_modules_dir}")
            print(f"📊 Processing statement: {statement_number}, attempt: {attempt_number}")

            return {
                "feature_modules_dir": feature_modules_dir,
                "stage1_metadata_path": stage1_metadata_path
            }

        except Exception as e:
            print(f"❌ Error setting up module update paths: {str(e)}")
            raise

    def read_and_decrypt_module(self, feature_name: str, module_path: str, migration_name: str, cloud_category: str) -> str:
        """
        Read and decrypt Python module from QMigrator conversion modules.

        Args:
            feature_name: Name of the feature
            module_path: Path to the module
            migration_name: Migration name
            cloud_category: Cloud category for path selection

        Returns:
            Decrypted module content as string
        """
        try:
            # Get base path based on cloud category
            if cloud_category.lower() == 'local':
                base_path = Config.Qbook_Local_Path
            else:
                base_path = Config.Qbook_Path

            # Construct full module path
            # module_path already contains the full path including .py file
            full_module_path = os.path.join(
                base_path,
                'Conversion_Modules',
                migration_name,
                module_path
            )

            print(f"📖 Reading module from: {full_module_path}")

            if not os.path.exists(full_module_path):
                print(f"⚠️ Module file not found: {full_module_path}")
                return ""

            # Decrypt the module using the same method as other nodes
            # Using hardcoded decrypt key from the existing system
            decrypt_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='
            decrypted_content = decrypt_conversion_file(full_module_path, decrypt_key)

            print(f"✅ Successfully read and decrypted module: {feature_name}")
            return decrypted_content

        except Exception as e:
            print(f"❌ Error reading module {feature_name}: {str(e)}")
            return ""

    def combine_features_with_deduplication(self, available_features: List[tuple], responsible_features: List[tuple]) -> List[tuple]:
        """
        Combine available features and responsible features, removing duplicates.
        If same feature exists in both lists, responsible_features takes priority.

        Args:
            available_features: Features from QMigrator available features
            responsible_features: Features identified as responsible from CSV

        Returns:
            Combined list of unique features with responsible features taking priority
        """
        try:
            # Create a dictionary to handle deduplication
            # Key: feature_name, Value: (feature_name, module_path)
            combined_dict = {}

            # First add available features
            for feature_name, module_path in available_features:
                combined_dict[feature_name] = (feature_name, module_path)

            # Then add responsible features (will overwrite duplicates)
            for feature_data in responsible_features:
                # Handle different tuple formats safely
                if len(feature_data) >= 2:
                    feature_name, module_path = feature_data[0], feature_data[1]

                    if feature_name in combined_dict:
                        print(f"🔄 Overriding {feature_name}: responsible feature takes priority")
                    combined_dict[feature_name] = (feature_name, module_path)
                else:
                    print(f"⚠️ Invalid responsible feature data: {feature_data}")

            # Convert back to list
            combined_features = list(combined_dict.values())

            print(f"🔗 Feature combination summary:")
            print(f"   📋 Available features: {len(available_features)}")
            print(f"   🎯 Responsible features: {len(responsible_features)}")
            print(f"   🔗 Combined unique features: {len(combined_features)}")

            return combined_features

        except Exception as e:
            print(f"❌ Error combining features: {str(e)}")
            # Fallback: return available features only
            return available_features

    def read_and_decrypt_module_from_full_path(self, feature_name: str, full_module_path: str) -> str:
        """
        Read and decrypt a module file from a full path.

        Args:
            feature_name: Name of the feature module
            full_module_path: Full path to the module file

        Returns:
            Decrypted module code

        Raises:
            Exception: If module cannot be found or decrypted
        """
        if not os.path.exists(full_module_path):
            raise Exception(f"Module file not found: {full_module_path}")

        # Read encrypted module content
        with open(full_module_path, 'r', encoding='utf-8') as f:
            encrypted_content = f.read()

        if not encrypted_content.strip():
            raise Exception(f"Module file is empty: {full_module_path}")

        # Decrypt the module using existing decryption function
        decryption_key = 'DA2OLixMhoOlKVdcq93TVM9rZ1kSDqZ3_223QmGK6jY='
        decrypted_content = decrypt_conversion_file(full_module_path, decryption_key)

        if not decrypted_content.strip():
            raise Exception(f"Module decryption resulted in empty content: {feature_name}")

        return decrypted_content

    def prepare_ai_analysis_context(self, statement_data: Dict, feature_name: str, module_path: str, module_content: str, migration_name: str = '', responsibility_reason: str = '', keywords: str = '') -> Dict:
        """
        Prepare context for AI analysis of module enhancement requirements.

        Args:
            statement_data: Current statement data with conversion details
            feature_name: Name of the feature being processed
            module_path: Path to the module
            module_content: Original module content
            migration_name: Migration name for database-specific terms
            responsibility_reason: AI-determined reason why this module is responsible for the conversion issue
            keywords: CSV keywords associated with this feature for enhancement context

        Returns:
            Context dictionary for AI analysis
        """
        try:
            # Extract statement information
            original_source_statement = statement_data.get('original_source_statement', '')
            original_target_statement = statement_data.get('original_target_statement', '')
            ai_converted_statement = statement_data.get('ai_converted_statement', '')
            original_deployment_error = statement_data.get('original_deployment_error', '')

            # Create simple context - AI can analyze everything itself
            context = {
                'feature_name': feature_name,
                'module_path': module_path,
                'original_module_code': module_content,
                'responsibility_reason': responsibility_reason,  # AI-determined responsibility reason from identification analysis
                'keywords': keywords,  # CSV keywords for enhancement prompts and module analysis
                'conversion_context': {
                    'original_source_statement': original_source_statement,
                    'original_target_statement': original_target_statement,
                    'ai_converted_statement': ai_converted_statement,
                    'original_deployment_error': original_deployment_error,
                    'migration_name': migration_name  # Add migration name for database-specific terms
                }
            }

            return context

        except Exception as e:
            print(f"❌ Error preparing AI context: {str(e)}")
            return {}

    def get_attempt_history_for_learning(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Get in-memory attempt history for learning from previous attempts.

        Args:
            state: Current workflow state containing attempt history

        Returns:
            Dict containing all previous attempts for learning
        """
        try:
            # Get attempt history from state
            attempt_history = getattr(state, 'attempt_history', [])

            print(f"📊 Found {len(attempt_history)} attempts in memory")

            if not attempt_history:
                print("📊 No previous attempts found for learning")
                return {'all_attempts': [], 'total_attempts': 0}

            # Return all attempts for learning
            all_attempts = attempt_history

            print(f"📊 Retrieved {len(all_attempts)} attempts for learning")

            return {
                'all_attempts': all_attempts,
                'total_attempts': len(attempt_history)
            }

        except Exception as e:
            print(f"⚠️ Error getting attempt history: {str(e)}")
            return {'all_attempts': [], 'total_attempts': 0}

    # Removed create_failure_pattern_summary function - no longer needed since we keep all attempts detailed

    def save_attempt_to_memory(self, state: Stage2WorkflowState, attempt_number: int, updated_modules: List[Dict[str, Any]], ai_comparison_feedback: str = None, final_output: str = None) -> Dict[str, Any]:
        """
        Save the current attempt to in-memory storage for future learning.
        Returns state update instead of mutating directly.

        Args:
            state: Current workflow state
            attempt_number: Current attempt number
            updated_modules: List of modules used in this attempt
            ai_comparison_feedback: Feedback from AI comparison explaining why it failed
            final_output: The final output produced by this attempt

        Returns:
            Dict containing attempt_history update
        """
        try:
            # Get current attempt history from state
            attempt_history = getattr(state, 'attempt_history', [])

            # Check if this attempt number already exists
            existing_attempt = any(
                attempt.get('attempt_number') == attempt_number
                for attempt in attempt_history
            )

            if existing_attempt:
                print(f"⚠️ Attempt {attempt_number} already exists in memory - skipping duplicate")
                return {}

            # Create attempt record
            attempt_record = {
                'attempt_number': attempt_number,
                'modules_used': [],
                'ai_feedback': ai_comparison_feedback or f"Attempt {attempt_number} failed - modules combination did not produce expected output",
                'final_output': final_output,
                'status': 'FAILED',
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }

            # Add module information
            for module in updated_modules:
                module_info = {
                    'module_name': module.get('feature_name', 'unknown'),
                    'module_code': module.get('updated_code', ''),
                    'file_path': module.get('updated_module_path', '')
                }
                attempt_record['modules_used'].append(module_info)

            # Append to attempt history
            attempt_history.append(attempt_record)

            print(f"💾 Saved attempt {attempt_number} to memory ({len(updated_modules)} modules)")
            print(f"📊 Total attempts in memory: {len(attempt_history)}")

            # Return state update instead of mutating directly
            return {"attempt_history": attempt_history}

        except Exception as e:
            print(f"⚠️ Error saving attempt to memory: {str(e)}")
            return {}

    def clear_attempt_history_memory(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Clear in-memory attempt history when moving to next statement.
        Returns state update instead of mutating directly.

        Args:
            state: Current workflow state

        Returns:
            Dict containing attempt_history update
        """
        try:
            print("🗑️ Cleared attempt history from memory for next statement")
            # Return state update instead of mutating directly
            return {"attempt_history": []}

        except Exception as e:
            print(f"⚠️ Error clearing attempt history from memory: {str(e)}")
            return {}



    def ai_enhance_module(self, context: Dict, ai_comparison_feedback: Optional[str] = None, attempt_history: Optional[Dict[str, Any]] = None) -> Dict:
        """
        Use AI to enhance the Python module based on conversion requirements with complete attempt history learning.

        Args:
            context: Analysis context with module and conversion details
            ai_comparison_feedback: Optional feedback from AI statement comparison
            attempt_history: Optional complete previous attempts with module combinations and feedback

        Returns:
            Enhancement result with updated code and analysis
        """
        try:
            print(f"🤖 Starting AI enhancement for module: {context.get('feature_name', 'Unknown')}")

            # Get database-specific terms for prompts (clean names without versions)
            migration_name = context.get('conversion_context', {}).get('migration_name', '')
            db_terms = get_database_specific_terms(migration_name) if migration_name else {}

            # Extract conversion context data
            conversion_context = context.get('conversion_context', {})

            # Get feature context from context (migration_name already extracted above)
            feature_name = context.get('feature_name', '')
            keywords = context.get('keywords', '')

            # Create AI prompt using the pattern-based analysis approach with complete attempt history
            formatted_prompt = create_module_enhancement_prompt(
                original_module_code=context.get('original_module_code', ''),
                qmigrator_target_statement=conversion_context.get('original_target_statement', ''),
                ai_corrected_statement=conversion_context.get('ai_converted_statement', ''),
                deployment_error=conversion_context.get('original_deployment_error', ''),
                ai_comparison_feedback=ai_comparison_feedback,
                attempt_history=attempt_history or [],
                migration_name=migration_name,
                feature_name=feature_name,
                keywords=keywords,
                db_terms=db_terms
            )

            # Get AI response using structured output
            llm_with_structure = self.llm.client.with_structured_output(ModuleEnhancementOutput)

            print("🤖 Calling AI for module enhancement...")
            ai_response = llm_with_structure.invoke(formatted_prompt)

            if ai_response and ai_response.enhanced_code:
                print("✅ AI enhancement completed successfully")
                print(f"📝 Analysis: {ai_response.analysis[:100]}...")

                # Get AI's assessment of whether code was actually changed
                ai_claimed_changed = getattr(ai_response, 'code_changed', False)

                # Also verify by comparing actual code content (dual validation)
                original_module_code = context.get('original_module_code', '')
                original_code_normalized = re.sub(r'\s+', ' ', original_module_code.strip())
                enhanced_code_normalized = re.sub(r'\s+', ' ', ai_response.enhanced_code.strip())
                actually_changed = original_code_normalized != enhanced_code_normalized

                # Use both AI assessment and actual comparison for final decision
                code_changed = ai_claimed_changed and actually_changed

                if ai_claimed_changed and actually_changed:
                    print(f"✅ Module code was successfully enhanced (AI confirmed + code verified)")
                elif ai_claimed_changed and not actually_changed:
                    print(f"⚠️ AI claimed enhancement but code is identical (AI error)")
                elif not ai_claimed_changed and actually_changed:
                    print(f"⚠️ Code changed but AI didn't report it (AI error)")
                else:
                    print(f"⚠️ Module code was not changed (AI confirmed + code verified)")

                enhancement_result = {
                    'enhanced_code': ai_response.enhanced_code,
                    'analysis': ai_response.analysis,
                    'code_changed': code_changed
                }

                return enhancement_result

            else:
                print("❌ AI enhancement failed - no valid response")
                return {}

        except Exception as e:
            print(f"❌ AI enhancement error: {str(e)}")
            return {}

    def save_updated_module(self, feature_name: str, module_path: str, updated_code: str, feature_modules_dir: str, attempt_number: int) -> str:
        """
        Save the updated module to Stage1_Metadata feature_modules directory with attempt tracking.

        Args:
            feature_name: Name of the feature
            module_path: Original module path to extract filename
            updated_code: Updated module code
            feature_modules_dir: Directory to save the module
            attempt_number: Current attempt number

        Returns:
            Path to saved module file
        """
        try:
            # Extract original filename from module_path (e.g., 'raisenotice.py' from 'Common/Statement/Pre/raisenotice.py')
            original_filename = os.path.basename(module_path)
            # Remove .py extension and add attempt number
            base_name = os.path.splitext(original_filename)[0]  # 'raisenotice'
            module_filename = f"{base_name}_attempt_{attempt_number}.py"  # 'raisenotice_attempt_1.py'
            module_file_path = os.path.join(feature_modules_dir, module_filename)

            # Save the updated module
            with open(module_file_path, 'w', encoding='utf-8') as file:
                file.write(updated_code)

            print(f"💾 Saved updated module for feature '{feature_name}': {module_file_path}")
            return module_file_path

        except Exception as e:
            print(f"❌ Error saving updated module: {str(e)}")
            raise

    def get_original_module_path(self, feature_name: str, module_path: str, migration_name: str, cloud_category: str) -> str:
        """
        Get the full path to the original module.

        Args:
            feature_name: Name of the feature
            module_path: Module path
            migration_name: Migration name
            cloud_category: Cloud category

        Returns:
            Full path to original module
        """
        try:
            if cloud_category.lower() == 'local':
                base_path = Config.Qbook_Local_Path
            else:
                base_path = Config.Qbook_Path

            return os.path.join(
                base_path,
                'Conversion_Modules',
                migration_name,
                module_path,
                f"{feature_name}.py"
            )

        except Exception as e:
            print(f"❌ Error getting original module path: {str(e)}")
            return ""

    def log_module_update_to_excel(self, excel_file_path: str, update_data: Dict, status: str):
        """
        Log module update information to Excel file using append_sheet_to_excel function.

        Args:
            excel_file_path: Path to Excel file
            update_data: Update data to log
            status: Status of the update (SUCCESS, FAILED, ERROR)
        """
        try:
            # Extract essential data for Excel logging
            statement_number = update_data.get('statement_number', '')
            feature_name = update_data.get('feature_name', '')
            module_path = update_data.get('module_path', '')
            updated_module_path = update_data.get('updated_module_path', '')
            original_code = update_data.get('original_code', '')
            updated_code = update_data.get('updated_code', '')
            enhancement_analysis = str(update_data.get('enhancement_analysis', ''))
            attempt_number = update_data.get('attempt_number', 1)
            error_details = update_data.get('error', '')
            code_changed = update_data.get('code_changed', False)

            # Get keywords from update_data if available
            feature_keywords = update_data.get('keywords', 'N/A')

            # Simple enhancement status - just Enhanced or Not Enhanced
            enhancement_status = "Enhanced" if code_changed else "Not Enhanced"

            # Prepare simplified data for Excel sheet (only essential fields that actually exist)
            excel_data = [{
                "Statement_Number": statement_number,
                "Feature_Name": feature_name,
                "Module_Path": module_path,
                "Feature_Keywords": feature_keywords,
                "Updated_Module_Path": updated_module_path,
                "Enhancement_Status": enhancement_status,  # Enhanced or Not Enhanced
                "Original_Module_Code": original_code,
                "Updated_Python_Module": updated_code,
                "Analysis": enhancement_analysis,
                "Attempt_Number": attempt_number
            }]

            # Debug: Print what we're logging to Excel
            print(f"📊 Excel logging - Enhancement_Status: {excel_data[0].get('Enhancement_Status')}")
            print(f"📊 Excel logging - Code_Changed: {excel_data[0].get('Code_Changed')}")

            # Use existing append_sheet_to_excel function (same pattern as other nodes)
            append_sheet_to_excel(excel_file_path, "Module_Updates", excel_data)

        except Exception as e:
            print(f"❌ Error logging to Excel: {str(e)}")







    def compare_ai_statements(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Enhanced Step 11: Compare AI Statements Node.

        Purpose: Compare AI corrected output vs applied updated modules output using AI analysis
        to determine if they have similar target functionality.

        Enhanced Logic:
        1. Get AI corrected statement from Stage 1
        2. Get applied modules statement from current processing
        3. Use AI to compare functional equivalence
        4. If equivalent: proceed to next statement
        5. If not equivalent: generate feedback and retry module updates

        Returns:
            Dict containing enhanced comparison results and feedback
        """
        print("\n" + "="*80)
        print("🔍 ENHANCED AI STATEMENT COMPARISON")
        print("="*80)

        try:
            # Get statement data from state
            approved_statements = getattr(state, 'approved_statements', [])
            current_statement_index = getattr(state, 'current_statement_index', 0)
            updated_ai_converted_statement = getattr(state, 'updated_ai_converted_statement', '')

            if not approved_statements or current_statement_index >= len(approved_statements):
                print("⚠️ No current statement found for comparison")
                return {"ai_statements_match": False}

            current_statement = approved_statements[current_statement_index]
            oracle_statement = current_statement.get('original_source_statement', '')
            ai_corrected_statement = current_statement.get('ai_converted_statement', '')
            deployment_error = current_statement.get('original_deployment_error', '')

            print(f"📝 Comparing statements for statement {current_statement_index + 1}")
            print(f"🔍 Oracle Source: {oracle_statement[:100]}...")
            print(f"🎯 AI Corrected: {ai_corrected_statement[:100]}...")
            print(f"🔧 Applied Modules: {updated_ai_converted_statement[:100]}...")

            if not updated_ai_converted_statement:
                print("⚠️ No applied modules statement found")
                return {"ai_statements_match": False}

            if not ai_corrected_statement:
                print("⚠️ No AI corrected statement found")
                return {"ai_statements_match": False}

            # Prepare statement data for AI comparison - only the two outputs that need to be compared
            statement_data = {
                'ai_converted_statement': ai_corrected_statement,
                'updated_ai_converted_statement': updated_ai_converted_statement
            }

            # Get migration name for database-specific terms
            migration_name = getattr(state, 'migration_name', 'Oracle_Postgres14')

            # Use AI to perform comparison between AI corrected output and applied modules output
            comparison_result = self.ai_compare_statement_functionality(
                statement_data=statement_data,
                migration_name=migration_name
            )

            statements_match = comparison_result.get('statements_match', False)

            print(f"📊 Statements Match: {'YES' if statements_match else 'NO'}")

            # Set AI comparison feedback and increment attempt if statements don't match
            ai_comparison_feedback = None
            current_attempt = getattr(state, 'current_attempt', 1)

            if not statements_match:
                ai_comparison_feedback = comparison_result.get('explanation', 'Statements do not have similar target functionality')
                print(f"📝 AI Comparison Feedback: {ai_comparison_feedback[:100]}...")

                # Save the complete attempt info when AI comparison fails
                updated_modules = getattr(state, 'updated_modules', [])
                if updated_modules:
                    print(f"💾 Saving failed attempt {current_attempt} to memory with {len(updated_modules)} modules")
                    self.save_attempt_to_memory(
                        state,
                        current_attempt,
                        updated_modules,
                        ai_comparison_feedback,
                        updated_ai_converted_statement
                    )


                else:
                    print(f"⚠️ No updated modules found to save for attempt {current_attempt}")

                # Increment attempt number for retry
                current_attempt = current_attempt + 1
                print(f"🔄 Incrementing attempt number to {current_attempt} for retry")

            # Excel logging for AI statement comparison with both outputs
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                excel_data = [{
                    "Source_Statement_Number": current_statement.get('source_statement_number', current_statement_index + 1),
                    "Attempt_Number": getattr(state, 'current_attempt', 1),  # Log current attempt, not incremented
                    "Statements_Match": "YES" if statements_match else "NO",
                    "AI_Corrected_Output": ai_corrected_statement,
                    "Applied_Modules_Output": updated_ai_converted_statement,
                    "Analysis": comparison_result.get('explanation', 'No detailed analysis available'),
                    "Timestamp": current_timestamp
                }]

                append_sheet_to_excel(excel_path, "AI_Statement_Comparison", excel_data)
                print(f"📋 Logged AI statement comparison for statement {current_statement_index + 1}")

            return {
                "ai_statements_match": statements_match,
                "ai_comparison_feedback": ai_comparison_feedback,
                "current_attempt": current_attempt,  # Update attempt number in state
                "attempt_history": getattr(state, 'attempt_history', [])  # Preserve attempt history
            }

        except Exception as e:
            print(f"❌ Enhanced AI statement comparison failed: {str(e)}")
            return {
                "ai_statements_match": False,
                "error": str(e)
            }

    def ai_compare_statement_functionality(self, statement_data: Dict[str, Any],
                                         migration_name: str) -> Dict[str, Any]:
        """
        Use AI to compare functional equivalence between AI corrected and applied modules statements.
        Follows the same pattern as other AI analysis functions in the codebase.

        Args:
            statement_data: Dictionary containing all statement information
            migration_name: Migration name for database-specific terms

        Returns:
            Dict containing detailed comparison results
        """
        try:
            # Extract only the two statements that need to be compared
            ai_corrected_statement = statement_data.get('ai_converted_statement', '')
            applied_modules_statement = statement_data.get('updated_ai_converted_statement', '')

            # Get database-specific terms for prompts (clean names without versions)
            db_terms = get_database_specific_terms(migration_name)

            # Create AI analysis prompt using prompts folder - only comparing the two outputs
            analysis_prompt = create_ai_statement_comparison_prompt(
                ai_corrected_statement=ai_corrected_statement,
                applied_modules_statement=applied_modules_statement,
                db_terms=db_terms
            )

            # Use structured output for reliable AI analysis (same pattern as other nodes)
            structured_llm = self.llm.client.with_structured_output(StatementComparisonOutput)
            ai_result = structured_llm.invoke(analysis_prompt)

            print(f"🧠 AI comparison result: {'MATCH' if ai_result.statements_match else 'NO MATCH'}")
            if not ai_result.statements_match:
                print(f"📝 Feedback: {ai_result.explanation[:100]}...")

            return {
                "statements_match": ai_result.statements_match,
                "explanation": ai_result.explanation,
                "transformation_guidance": ai_result.transformation_guidance.dict() if ai_result.transformation_guidance else {}
            }

        except Exception as e:
            print(f"❌ AI comparison failed: {str(e)}")
            return {
                "statements_match": False,
                "explanation": f"AI comparison failed: {str(e)}. Manual review required."
            }



    def more_statements_decision(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 12: More Statements Decision Node.

        Purpose: Determine if more statements need processing or if current statement needs retry.

        This function implements the statement-by-statement processing logic:
        - Process one statement through entire workflow
        - If statement succeeds: move to next statement
        - If statement fails and attempts < max: retry current statement
        - If statement fails and attempts = max: move to next statement
        - If no more statements: complete workflow

        Returns:
            Dict with workflow control flags
        """
        print("🔄 Starting More Statements Decision...")

        try:
            # Get current state information from new pipeline
            iteration_action = getattr(state, 'iteration_action', None)
            statements_match = getattr(state, 'statements_match', False)
            current_attempt = getattr(state, 'current_attempt', 1)
            max_attempts = getattr(state, 'max_attempts', 5)
            current_statement_index = getattr(state, 'current_statement_index', 0)

            # Get current statement data and total count
            combined_data = getattr(state, 'available_features_with_statements', [])
            total_statements = len(combined_data)
            current_statement_data = combined_data[current_statement_index] if combined_data and current_statement_index < len(combined_data) else {}

            print(f"📊 Current Status:")
            print(f"   📝 Statement: {current_statement_index + 1}/{total_statements}")
            print(f"   🔄 Attempt: {current_attempt}/{max_attempts}")
            print(f"   ✅ Statements Match: {statements_match}")
            print(f"   🎯 Iteration Action: {iteration_action}")

            # Excel logging for statement decision tracking
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                current_timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Decision logic based on new pipeline iteration control
            if iteration_action == "proceed" or statements_match:
                # Current statement succeeded, check if more statements exist
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"✅ Statement {current_statement_index + 1} completed successfully")
                    print(f"📝 Moving to next statement {next_index + 1}/{total_statements}")

                    # Clear attempt history for completed statement is handled in return data

                    # Log successful statement completion
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "MOVE_TO_NEXT",
                            "Status": "SUCCESS",
                            "Analysis": f"Statement completed successfully after {current_attempt} attempt(s)",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged successful completion of statement {current_statement_index + 1}")

                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "validation_attempt": 1,  # Reset validation attempts for new statement
                        "execution_attempt": 1,  # Reset execution attempts for new statement
                        "validation_feedback": None,  # Clear validation feedback for new statement
                        "ai_comparison_feedback": None,  # Clear AI comparison feedback for new statement
                        "execution_feedback": None,  # Clear execution feedback for new statement
                        "enhancement_feedback_for_retry": None,  # Clear enhancement feedback for new statement
                        "current_statement": None,  # Clear current statement data
                        "responsible_features": None,  # Clear responsible features
                        "responsible_procedures": None,  # Clear responsible procedures
                        "updated_modules": None,  # Clear updated modules
                        "attempt_history": [],  # Clear attempt history for new statement

                        # Clear new pipeline state fields for new statement
                        "module_categories": None,
                        "pre_processed_output": None,
                        "driver_module_code": None,
                        "driver_module_path": None,
                        "enhanced_driver_code": None,
                        "enhanced_driver_path": None,
                        "decomposed_modules": None,
                        "validation_results": None,
                        "validation_passed": False,
                        "execution_success": False,
                        "final_output": None,
                        "statements_match": False,
                        "iteration_action": None,

                        "updated_ai_converted_statement": None,  # Clear updated statement
                        "current_statement_id": None,  # Clear statement ID


                        "ai_statements_match": False,  # Reset comparison flag
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed successfully!")

                    # Log workflow completion
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "WORKFLOW_COMPLETE",
                            "Status": "SUCCESS",
                            "Analysis": f"All {total_statements} statements processed successfully",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged workflow completion")

                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }
            elif iteration_action == "retry" or (not statements_match and current_attempt < max_attempts):
                # Current statement failed, retry enhancement with feedback
                print(f"❌ Statement {current_statement_index + 1} failed - retrying enhancement with feedback")
                print(f"🔢 Attempt tracking: current={current_attempt}, max={max_attempts}")

                # Log retry decision
                if excel_path:
                    excel_data = [{
                        "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                        "Attempt_Number": current_attempt,
                        "Decision": "RETRY_ENHANCEMENT",
                        "Status": "RETRY",
                        "Analysis": f"Enhancement failed, retrying with feedback (attempt {current_attempt}/{max_attempts})",
                        "Timestamp": current_timestamp
                    }]
                    append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                    print(f"📋 Logged retry decision for statement {current_statement_index + 1}")

                return {
                    "workflow_action": "retry_current"
                }
            elif iteration_action == "fail" or (not statements_match and current_attempt >= max_attempts):
                # Max attempts reached or explicit failure, move to next statement
                print(f"🛑 Statement {current_statement_index + 1} failed after {current_attempt} attempts")
                if current_statement_index < total_statements - 1:
                    next_index = current_statement_index + 1
                    print(f"📝 Moving to next statement {next_index + 1}/{total_statements}")

                    # Clear attempt history for failed statement is handled in return data

                    # Log max attempts reached, moving to next
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "MOVE_TO_NEXT_MAX_ATTEMPTS",
                            "Status": "FAILED",
                            "Analysis": f"Statement failed after {max_attempts} attempts, moving to next statement",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged max attempts reached for statement {current_statement_index + 1}")

                    return {
                        "current_statement_index": next_index,
                        "current_attempt": 1,  # Reset attempts for new statement
                        "validation_feedback": None,  # Clear validation feedback for new statement
                        "ai_comparison_feedback": None,  # Clear AI comparison feedback for new statement
                        "execution_feedback": None,  # Clear execution feedback for new statement
                        "enhancement_feedback_for_retry": None,  # Clear enhancement feedback for new statement
                        "current_statement": None,  # Clear current statement data
                        "responsible_features": None,  # Clear responsible features
                        "responsible_procedures": None,  # Clear responsible procedures
                        "updated_modules": None,  # Clear updated modules
                        "attempt_history": [],  # Clear attempt history for new statement

                        # Clear new pipeline state fields for new statement
                        "module_categories": None,
                        "pre_processed_output": None,
                        "driver_module_code": None,
                        "driver_module_path": None,
                        "enhanced_driver_code": None,
                        "enhanced_driver_path": None,
                        "decomposed_modules": None,
                        "validation_results": None,
                        "validation_passed": False,
                        "execution_success": False,
                        "final_output": None,
                        "statements_match": False,
                        "iteration_action": None,

                        "updated_ai_converted_statement": None,  # Clear updated statement
                        "current_statement_id": None,  # Clear statement ID


                        "ai_statements_match": False,  # Reset comparison flag
                        "workflow_action": "next_statement"
                    }
                else:
                    print(f"🎉 All {total_statements} statements processed (some with max attempts)")

                    # Log final workflow completion with some failures
                    if excel_path:
                        excel_data = [{
                            "Source_Statement_Number": current_statement_data.get('source_statement_number', current_statement_index + 1),
                            "Attempt_Number": current_attempt,
                            "Decision": "WORKFLOW_COMPLETE_WITH_FAILURES",
                            "Status": "COMPLETED",
                            "Analysis": f"All {total_statements} statements processed, some reached max attempts",
                            "Timestamp": current_timestamp
                        }]
                        append_sheet_to_excel(excel_path, "Statement_Decisions", excel_data)
                        print(f"📋 Logged final workflow completion")

                    return {
                        "workflow_action": "complete",
                        "workflow_completed": True
                    }
            else:
                # Unexpected state - default to completion
                print(f"⚠️ Unexpected state in more_statements_decision - defaulting to completion")
                print(f"   📊 iteration_action: {iteration_action}")
                print(f"   ✅ statements_match: {statements_match}")
                print(f"   🔄 current_attempt: {current_attempt}/{max_attempts}")

                return {
                    "workflow_action": "complete",
                    "workflow_completed": True
                }

        except Exception as e:
            print(f"❌ More Statements Decision failed: {str(e)}")
            return {
                "workflow_action": "complete",
                "error": str(e)
            }







    def get_module_code_for_feature(self, state: Stage2WorkflowState, feature_name: str,
                                   current_statement_number: int, current_attempt: int,
                                   original_module_path: str, updated_modules_map: Dict[str, Any]) -> str:
        """
        Get module code - updated version from qm_feature_modules folder based on current attempt.

        Args:
            state: Stage2WorkflowState containing migration context
            feature_name: Name of the feature module
            current_statement_number: Current statement number being processed
            current_attempt: Current attempt number
            original_module_path: Path to original module
            updated_modules_map: Map of updated modules by feature name

        Returns:
            Decrypted module code ready for execution

        Raises:
            Exception: If module cannot be found or read
        """
        # Check if we have an updated module for this feature
        if feature_name in updated_modules_map:
            # Construct updated module path based on current attempt using cloud_category (same pattern as parent nodes)
            cloud_category = getattr(state, 'cloud_category', 'cloud')

            # Get Stage1_Metadata path based on cloud category
            if cloud_category.lower() == 'local':
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get dynamic folder name based on process type
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            qm_feature_modules_folder = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            updated_module_filename = f"{feature_name}_attempt_{current_attempt}.py"
            updated_module_path = os.path.join(
                qm_feature_modules_folder,
                str(current_statement_number),
                updated_module_filename
            )

            if os.path.exists(updated_module_path):
                print(f"🔧 Using updated module for {feature_name} (attempt {current_attempt}): {updated_module_path}")
                # Read updated module as plain text (not encrypted)
                try:
                    with open(updated_module_path, 'r', encoding='utf-8') as f:
                        updated_code = f.read()
                    if updated_code.strip():
                        return updated_code
                    else:
                        raise Exception(f"Updated module file is empty: {updated_module_path}")
                except Exception as e:
                    print(f"❌ Error reading updated module {feature_name}: {str(e)}")
                    raise Exception(f"Failed to read updated module {feature_name}: {str(e)}")
            else:
                print(f"📝 No updated module found for {feature_name} attempt {current_attempt}, checking if feature was updated in previous attempts")
                # Feature was identified as needing update but no file for current attempt exists
                # This means we should use original module
                pass

        # Use original module (original_module_path is relative path)
        print(f"📁 Using original module for {feature_name}: {original_module_path}")
        return self.read_and_decrypt_module(
            feature_name, original_module_path, state.migration_name, state.cloud_category
        )



    def apply_feature_module(self, statement: str, feature_name: str, module_code: str, state: Stage2WorkflowState) -> str:
        """
        Execute a feature module to transform the statement using importlib.util.
        """
        if not module_code.strip():
            raise Exception(f"Empty module code for {feature_name}")

        try:
            spec = importlib.util.spec_from_loader(feature_name, loader=None, origin="<in-memory>")
            module = importlib.util.module_from_spec(spec)

            # Execute the module code
            exec(module_code, module.__dict__)

        except Exception as exec_error:
            print(f"⚠️ Module execution error for {feature_name}: {str(exec_error)}")
            return statement

        # Try to call the function if it exists
        if hasattr(module, feature_name) and callable(getattr(module, feature_name)):
            try:
                schema_name = getattr(state, 'schema_name', None)
                if not schema_name:
                    print(f"⚠️ No schema_name found in state for {feature_name}")
                    return statement

                result = getattr(module, feature_name)(statement, schema_name)
                print(f"🔧 Module {feature_name} execution result: input_length={len(statement)}, output_length={len(str(result))}, same_content={result == statement}")
                return str(result)
            except Exception as func_error:
                print(f"⚠️ Function call error for {feature_name}: {str(func_error)}")
                return statement
        else:
            # Fallback: Try to get transformed statement from variables
            transformed_statement = (
                getattr(module, 'output_statement', None) or
                getattr(module, 'pre_output', None) or
                getattr(module, 'source_data', None) or
                statement
            )
            return str(transformed_statement)



    def complete_processing(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Step 13: Complete Processing Node.

        Purpose: Finalize Stage 2 workflow and prepare outputs.

        Returns:
            Dict containing final workflow results
        """
        print("🔄 Starting Complete Processing...")

        try:
          

            print("✅ Stage 2 workflow completed!")

            return {
                "workflow_completed": True,
                "final_excel_path": state.final_qbook_excel_path if state.final_qbook_excel_path else ""
            }

        except Exception as e:
            error_msg = f"❌ Complete Processing failed: {str(e)}"
            print(error_msg)
            return {
                "error": error_msg,
                "workflow_completed": False,
                "final_excel_path": ""
            }

    # ==================== UTILITY FUNCTIONS ====================





    def cleanup_process_feature_modules(self, state: Stage2WorkflowState) -> None:
        """
        Clean process-type-specific feature modules directory for fresh start when reprocessing.

        Deletes based on process type:
        - QMigrator: qbookv2/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/qm_feature_modules
        - QBook: qbookv2/Stage1_Metadata/{migration_name}/{schema_name}/{object_type}/{object_name}/qbook_feature_modules

        Args:
            state: Stage2WorkflowState containing object details and process type
        """
        try:
            # Determine paths based on cloud_category
            if state.cloud_category.lower() == "local":
                stage1_metadata_path = Config.Qbook_Local_Path
            else:
                stage1_metadata_path = Config.Qbook_Path

            # Get process-type-specific folder name
            feature_modules_folder = self.get_feature_modules_folder_name(state.process_type)

            # Build process-specific feature modules directory path
            feature_modules_dir = os.path.join(
                stage1_metadata_path,
                "Stage1_Metadata",
                state.migration_name,
                state.schema_name,
                state.objecttype,
                state.object_name,
                feature_modules_folder
            )

            if os.path.exists(feature_modules_dir):
                print(f"🗑️ Cleaning {state.process_type} feature modules directory: {feature_modules_dir}")
                shutil.rmtree(feature_modules_dir)
                print(f"🗑️ Successfully deleted {feature_modules_folder} directory")
            else:
                print(f"📁 {feature_modules_folder} directory doesn't exist (first run): {feature_modules_dir}")

        except Exception as e:
            print(f"⚠️ Warning: Could not clean {state.process_type} feature modules directory: {str(e)}")


    def get_feature_modules_folder_name(self, process_type: str) -> str:
        """
        Get dynamic folder name based on process type.

        Args:
            process_type: "qmigrator" or "qbook"

        Returns:
            "qm_feature_modules" for qmigrator, "qbook_feature_modules" for qbook
        """
        if process_type == "qmigrator":
            return "qm_feature_modules"
        else:
            return "qbook_feature_modules"

    # ==================== NEW PIPELINE NODES ====================

    def categorize_execution_modules(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 6: Categorize Execution Modules - Pipeline Preparation

        Purpose:
            Organizes all available conversion modules into three execution categories:
            pre-execution, responsible, and post-execution. This categorization enables
            the sequential pipeline approach where modules are executed in proper order.

        Business Logic:
            1. Pre-execution: Features that appear before the first responsible feature
            2. Responsible: Features identified by AI analysis as needing enhancement
            3. Post-execution: Features from post_features column (cleanup, formatting)

        Categorization Rules:
            - Uses Excel post_features column as authoritative source for post-processing
            - Identifies first responsible feature position in available_features list
            - Everything before first responsible = pre-execution
            - Responsible features = those identified by AI analysis
            - Post features = explicitly defined post-processing modules

        Input Requirements:
            - state.available_features_with_statements: Combined statement and feature data
            - state.responsible_features: AI-identified responsible features
            - state.current_statement_index: Current statement being processed

        Output:
            - module_categories: Dict with pre_execution, responsible, post_execution lists
            - categorization_summary: Summary of module counts per category
            - execution_order: Planned execution sequence

        Next Nodes:
            - Success → execute_pre_features
            - No modules → complete_processing

        Error Handling:
            - Missing statement data validation
            - Empty feature lists handling
            - Categorization logic errors
        """
        print("\n" + "="*80)
        print("📋 CATEGORIZE EXECUTION MODULES")
        print("="*80)

        try:
            # Get current statement data
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])

            if not available_features_with_statements or current_statement_index >= len(available_features_with_statements):
                return {"success": False, "error": "No current statement data available"}

            current_statement = available_features_with_statements[current_statement_index]

            # Get available features from Excel (QMigrator results)
            available_features = current_statement.get('available_features', [])

            # Get responsible features from CSV analysis
            responsible_features = getattr(state, 'responsible_features', [])

            # Get post features (exception handling, etc.)
            post_features = self.get_post_processing_features(current_statement)

            # Categorize modules
            module_categories = self.categorize_modules_by_execution_order(
                available_features, responsible_features, post_features
            )

            # Log to Excel
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                self.log_pipeline_preparation_to_excel(excel_path, {
                    'operation': 'MODULE_CATEGORIZATION',
                    'statement_number': current_statement_index + 1,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'module_categories': module_categories,
                    'categorization_summary': f"Pre: {len(module_categories['pre_execution'])}, Responsible: {len(module_categories['responsible'])}, Post: {len(module_categories['post_execution'])}"
                })

            print(f"✅ Module categorization completed:")
            print(f"   📋 Pre-execution: {len(module_categories['pre_execution'])} modules")
            print(f"   🎯 Responsible: {len(module_categories['responsible'])} modules")
            print(f"   📤 Post-execution: {len(module_categories['post_execution'])} modules")

            return {
                "module_categories": module_categories,
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in categorize_execution_modules: {str(e)}")
            return {"success": False, "error": str(e)}

    def execute_pre_features(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 7: Execute Pre-Features.

        Purpose: Execute pre-processing modules sequentially to prepare input
        for responsible module enhancement.
        """
        print("\n" + "="*80)
        print("🔄 EXECUTE PRE-FEATURES")
        print("="*80)

        try:
            # Get module categories and current statement
            module_categories = getattr(state, 'module_categories', {})
            pre_execution_modules = module_categories.get('pre_execution', [])

            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])
            current_statement = available_features_with_statements[current_statement_index]

            # Get original statement after type casting
            original_statement = current_statement.get('statement_after_typecasting',
                                                     current_statement.get('original_source_statement', ''))

            # Execute pre-modules sequentially
            current_output = original_statement
            execution_log = []

            print(f"🔍 Starting pre-feature execution with {len(pre_execution_modules)} modules")
            print(f"📝 Initial input: {original_statement[:100]}...")

            for module_name, module_path in pre_execution_modules:
                # Read and decrypt module
                module_code = self.read_and_decrypt_module(module_name, module_path,
                                                         state.migration_name, state.cloud_category)

                if module_code:
                    previous_output = current_output
                    print(f"🔍 Executing pre-module: {module_name}")

                    # Execute module
                    current_output = self.apply_feature_module(current_output, module_name, module_code, state)

                    # Log transformation
                    transformation_applied = current_output != previous_output
                    execution_log.append({
                        'module_name': module_name,
                        'module_path': module_path,
                        'transformation_applied': transformation_applied,
                        'input_length': len(previous_output),
                        'output_length': len(current_output)
                    })

                    print(f"   ✅ {module_name}: {'Applied transformation' if transformation_applied else 'No changes'}")

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'PRE_EXECUTION_COMPLETE',
                    'pipeline_phase': 'Pre-execution',
                    'success': True,
                    'notes': f"Executed {len(pre_execution_modules)} pre-execution modules. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log each pre-execution module
                for i, log_entry in enumerate(execution_log):
                    self.log_module_processing(excel_path, {
                        'statement_number': current_statement_index + 1,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': 'PRE_EXECUTION',
                        'module_name': log_entry['module_name'],
                        'module_type': 'pre',
                        'input_code': f"Input length: {log_entry['input_length']} chars",
                        'output_code': f"Output length: {log_entry['output_length']} chars",
                        'status': 'EXECUTED',
                        'changes_made': log_entry['transformation_applied'],
                        'file_path': log_entry['module_path'],
                        'module_order': i + 1,
                        'additional_info': f"Transformation applied: {log_entry['transformation_applied']}"
                    })

                # Log to Pipeline_Preparation sheet with full input/output
                self.log_pipeline_preparation_to_excel(excel_path, {
                    'operation': 'PRE_EXECUTION',
                    'statement_number': current_statement_index + 1,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'original_input': original_statement,  # Full original statement
                    'pre_processed_output': current_output,  # Full pre-processed output
                    'execution_log': execution_log,
                    'modules_executed': len(pre_execution_modules)
                })

            print(f"✅ Pre-feature execution completed")
            print(f"📤 Pre-processed output: {current_output[:100]}...")

            return {
                "pre_processed_output": current_output,
                "execution_log": execution_log,
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in execute_pre_features: {str(e)}")
            return {"success": False, "error": str(e)}

    # ==================== HELPER FUNCTIONS FOR NEW PIPELINE ====================

    def get_post_processing_features(self, current_statement: Dict[str, Any]) -> List[tuple]:
        """Get post-processing features from the post_features column in CSV."""
        # Get post_features directly from the CSV data
        post_features = current_statement.get('post_features', [])

        # Ensure it's a list of tuples
        if isinstance(post_features, list):
            return post_features
        else:
            return []

    def categorize_modules_by_execution_order(self, available_features: List[tuple],
                                            responsible_features: List[tuple],
                                            post_features: List[tuple]) -> Dict[str, List[tuple]]:
        """
        Categorize modules into pre/responsible/post execution order.

        Logic:
        - Pre-execution: Features that come BEFORE the first responsible module in available_features
        - Responsible: All modules identified by AI as responsible
        - Post-execution: All post_features from Excel
        """

        # Convert responsible features to set for easier lookup
        responsible_names = set()
        for feat in responsible_features:
            if isinstance(feat, tuple) and len(feat) >= 1:
                responsible_names.add(feat[0])
            elif isinstance(feat, dict):
                responsible_names.add(feat.get('module_name', ''))

        pre_execution = []
        responsible = []
        post_execution = []

        # 1. All post_features from Excel go to post_execution
        for post_feature in post_features:
            if isinstance(post_feature, tuple) and len(post_feature) >= 1:
                post_execution.append(post_feature)

        # 2. Find the position of the first responsible module in available_features
        first_responsible_pos = None
        for i, feature_tuple in enumerate(available_features):
            if len(feature_tuple) >= 1 and feature_tuple[0] in responsible_names:
                first_responsible_pos = i
                break

        # 3. Categorize available_features based on position relative to first responsible module
        for i, feature_tuple in enumerate(available_features):
            if len(feature_tuple) >= 1:
                feature_name = feature_tuple[0]

                if first_responsible_pos is not None and i < first_responsible_pos:
                    # This feature comes before the first responsible module → pre-execution
                    pre_execution.append(feature_tuple)
                elif first_responsible_pos is not None and i >= first_responsible_pos:
                    # This feature is at or after the first responsible module → responsible
                    responsible.append(feature_tuple)
                elif first_responsible_pos is None and responsible_names:
                    # No responsible modules in available_features, but we have responsible modules identified
                    # So all available_features are pre-execution
                    pre_execution.append(feature_tuple)

        # 3. Add AI-identified responsible features that aren't in available_features
        # (These come from CSV mapping or global module analysis)
        for resp_feature in responsible_features:
            feature_name = None
            if isinstance(resp_feature, tuple) and len(resp_feature) >= 1:
                feature_name = resp_feature[0]
                feature_tuple = resp_feature
            elif isinstance(resp_feature, dict):
                feature_name = resp_feature.get('module_name', '')
                feature_tuple = (resp_feature.get('module_name', ''), resp_feature.get('module_path', ''))

            # Check if this responsible feature is already in the responsible list
            if feature_name and not any(r[0] == feature_name for r in responsible if len(r) >= 1):
                responsible.append(feature_tuple)

        return {
            'pre_execution': pre_execution,
            'responsible': responsible,
            'post_execution': post_execution
        }

    def log_pipeline_preparation_to_excel(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log pipeline preparation results (categorization + pre-execution) to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            excel_data = [{
                "Statement_Number": data.get('statement_number', 0),
                "Attempt_Number": data.get('attempt_number', 1),
                "Operation": data.get('operation', ''),
                "Details": str(data.get('categorization_summary', data.get('modules_executed', ''))),
                "Input": data.get('original_input', ''),  # Full input, no truncation
                "Output": data.get('pre_processed_output', ''),  # Full output, no truncation
                "Execution_Log": str(data.get('execution_log', data.get('module_categories', ''))),
                "Timestamp": current_timestamp
            }]

            append_sheet_to_excel(excel_path, "Pipeline_Preparation", excel_data)
            print(f"📊 Logged {data.get('operation')} to Pipeline_Preparation sheet")

        except Exception as e:
            print(f"❌ Error logging pipeline preparation to Excel: {str(e)}")

    def combine_driver_module(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 8: Combine Driver Module.

        Purpose: Combine responsible modules into single driver module with boundary markers.
        """
        print("\n" + "="*80)
        print("🤖 COMBINE DRIVER MODULE")
        print("="*80)

        try:
            # Get responsible modules
            module_categories = getattr(state, 'module_categories', {})
            responsible_modules = module_categories.get('responsible', [])

            if not responsible_modules:
                return {"success": False, "error": "No responsible modules to combine"}

            # Read and combine modules
            combined_modules = []
            attempt_number = getattr(state, 'current_attempt', 1)

            print(f"🔍 Combining {len(responsible_modules)} responsible modules")

            for module_data in responsible_modules:
                # Handle different data structures
                if isinstance(module_data, tuple) and len(module_data) >= 2:
                    module_name = module_data[0]
                    module_path = module_data[1]
                elif isinstance(module_data, dict):
                    module_name = module_data.get('module_name', '')
                    module_path = module_data.get('module_path', '')
                else:
                    print(f"⚠️ Unexpected module data format: {module_data}")
                    continue
                # Read and decrypt original module
                module_code = self.read_and_decrypt_module(module_name, module_path,
                                                         state.migration_name, state.cloud_category)

                if module_code:
                    # Add boundary markers
                    module_with_markers = f"""
# === MODULE: {module_name} START ===
{module_code}
# === MODULE: {module_name} END ===
"""
                    combined_modules.append(module_with_markers)
                    print(f"   ✅ Added {module_name} to driver module")

            # Combine all modules into single driver
            driver_module_code = "\n\n".join(combined_modules)

            # Save driver module
            driver_module_path = self.save_driver_module(driver_module_code, attempt_number, state)

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Extract module names safely from different data structures
                module_names = []
                for module_data in responsible_modules:
                    if isinstance(module_data, tuple) and len(module_data) >= 1:
                        module_names.append(module_data[0])
                    elif isinstance(module_data, dict):
                        module_names.append(module_data.get('module_name', ''))

                # Get module counts from state
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Log pipeline overview with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                    'attempt_number': attempt_number,
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'DRIVER_COMBINATION_COMPLETE',
                    'pipeline_phase': 'Driver Combination',
                    'success': True,
                    'notes': f"Combined {responsible_count} responsible modules: {', '.join(module_names)}. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log each module combination
                for i, module_data in enumerate(responsible_modules):
                    module_name = module_data[0] if isinstance(module_data, tuple) else module_data.get('module_name', '')
                    module_path = module_data[1] if isinstance(module_data, tuple) and len(module_data) >= 2 else module_data.get('module_path', '')

                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': attempt_number,
                        'operation': 'DRIVER_COMBINATION',
                        'module_name': module_name,
                        'module_type': 'responsible',
                        'input_code': f"Original module from: {module_path}",
                        'output_code': driver_module_code,  # Full combined driver code
                        'status': 'COMBINED',
                        'changes_made': True,
                        'file_path': driver_module_path,
                        'module_order': i + 1,
                        'additional_info': f"Combined into driver module with boundary markers"
                    })

            print(f"✅ Driver module combination completed")
            print(f"📁 Driver module saved: {driver_module_path}")

            return {
                "driver_module_code": driver_module_code,
                "driver_module_path": driver_module_path,
                "combined_modules_count": len(responsible_modules),
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in combine_driver_module: {str(e)}")
            return {"success": False, "error": str(e)}

    def enhance_driver_module(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 9: Enhance Driver Module - AI-Driven Module Enhancement Hub

        Purpose:
            Central enhancement node that uses AI to improve combined driver modules based on
            comprehensive feedback from validation, execution, and comparison failures.
            This is the primary retry target for all pipeline validation loops.

        Business Logic:
            1. Receives feedback from validate_module_enhancement, execute_complete_pipeline,
               and ai_statement_comparison_pipeline nodes
            2. Uses AI to analyze feedback and enhance module logic accordingly
            3. Implements iterative improvement based on specific failure patterns
            4. Maintains enhancement history for learning from previous attempts

        Enhancement Process:
            - Analyzes combined driver module code from previous step
            - Incorporates responsibility context from feature identification
            - Applies validation feedback (syntax, logic errors)
            - Applies execution feedback (runtime errors, deployment issues)
            - Applies comparison feedback (functional equivalence failures)
            - Uses deployment error context for targeted fixes

        Feedback Integration:
            - validation_feedback: Syntax and logic validation errors
            - execution_feedback: Runtime and deployment errors
            - comparison_feedback: Functional equivalence analysis
            - responsibility_context: Original AI analysis reasoning

        Input Requirements:
            - state.combined_driver_code: Combined module code to enhance
            - state.pre_processed_output: Pre-processing results for context
            - Various feedback fields from validation/execution/comparison failures
            - state.current_attempt: Attempt counter for enhancement tracking

        Output:
            - enhanced_driver_code: AI-enhanced module code
            - enhancement_summary: Summary of changes made
            - attempt_number: Updated attempt counter
            - enhancement_reasoning: AI explanation of improvements

        Next Nodes:
            - Success → decompose_enhanced_module
            - Enhancement failure → enhancement_iteration_control

        Error Handling:
            - AI enhancement failures with fallback strategies
            - Code generation errors
            - Feedback parsing errors
        """
        print("\n" + "="*80)
        print("🔧 ENHANCE DRIVER MODULE")
        print("="*80)

        try:
            # Get driver module code
            driver_module_code = getattr(state, 'driver_module_code', '')
            if not driver_module_code:
                return {"success": False, "error": "No driver module code available"}

            # Build enhancement context
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])
            current_statement = available_features_with_statements[current_statement_index]

            # Get responsible features for context
            responsible_features = getattr(state, 'responsible_features', [])

            # Build responsibility context for prompt compatibility
            responsible_modules_with_reasoning = []
            for feature_tuple in responsible_features:
                if len(feature_tuple) >= 3:
                    module_info = {
                        'module_name': feature_tuple[0],
                        'module_path': feature_tuple[1],
                        'responsibility_reason': feature_tuple[2],
                        'keywords': feature_tuple[3] if len(feature_tuple) > 3 else ''
                    }
                    responsible_modules_with_reasoning.append(module_info)

            # Get pre-processed output from state (critical for enhancement)
            pre_processed_output = getattr(state, 'pre_processed_output',
                                         current_statement.get('statement_after_typecasting', ''))

            # Uniform enhancement context with standardized field names
            enhancement_context = {
                # Core transformation context (used by sequential_enhancement_prompt)
                'original_input_statement': current_statement.get('statement_after_typecasting', ''),
                'pre_processed_input': pre_processed_output,  # Current state after pre-processing
                'expected_final_output': current_statement.get('ai_converted_statement', ''),

                # Error and deployment context
                'deployment_error': current_statement.get('original_deployment_error', ''),
                'current_attempt': getattr(state, 'current_attempt', 1),

                # Module execution context
                'pre_execution_modules': [mod[0] for mod in getattr(state, 'module_categories', {}).get('pre_execution', [])],

                # Responsibility context (used by build_responsibility_section)
                'responsible_modules_with_reasoning': responsible_modules_with_reasoning,
                'responsibility_summary': f"Identified {len(responsible_modules_with_reasoning)} responsible modules for enhancement",

                # Database terms (dynamic from migration_name)
                'db_terms': get_database_specific_terms(state.migration_name),

                # Output comparison context (using correct available data)
                'ai_corrected_output': current_statement.get('ai_converted_statement', '')
            }

            # COMBINED FEEDBACK INTEGRATION
            feedback_sources = []

            # Validation feedback (highest priority)
            if hasattr(state, 'validation_feedback') and state.validation_feedback:
                enhancement_context['validation_feedback'] = state.validation_feedback
                feedback_sources.append('validation')
                print("🔄 USING VALIDATION FEEDBACK FOR ENHANCEMENT")
                print(f"📋 VALIDATION FEEDBACK RECEIVED FROM validate_module_enhancement:")
                print(f"   ❌ Failed Modules: {state.validation_feedback.get('failed_modules', [])}")
                print(f"   🐛 Validation Errors: {state.validation_feedback.get('validation_errors', [])}")
                print(f"   🔧 Retry Guidance: {state.validation_feedback.get('retry_guidance', '')}")
                print(f"   🎯 Enhancement Strategy: Fix syntax errors and boundary marker issues")
                print(f"   📈 Attempt Number: {getattr(state, 'current_attempt', 1)} → {getattr(state, 'current_attempt', 1) + 1}")
                # Increment attempt for validation retry
                attempt_number = getattr(state, 'current_attempt', 1) + 1

            # Execution feedback (medium priority)
            elif hasattr(state, 'execution_feedback') and state.execution_feedback:
                enhancement_context['execution_feedback'] = state.execution_feedback
                feedback_sources.append('execution')
                print("🔄 USING EXECUTION FEEDBACK FOR ENHANCEMENT")
                print(f"📋 EXECUTION FEEDBACK RECEIVED FROM execute_complete_pipeline:")
                print(f"   💥 Execution Errors: {state.execution_feedback.get('execution_errors', [])}")
                print(f"   📍 Failed Phase: {state.execution_feedback.get('failed_phase', '')}")
                print(f"   🔧 Retry Guidance: {state.execution_feedback.get('retry_guidance', '')}")
                print(f"   🎯 Enhancement Strategy: Fix runtime errors in enhanced modules")
                print(f"   📈 Attempt Number: {getattr(state, 'current_attempt', 1)} → {getattr(state, 'current_attempt', 1) + 1}")
                # Increment attempt for execution retry
                attempt_number = getattr(state, 'current_attempt', 1) + 1

            # Comparison feedback (lowest priority)
            elif hasattr(state, 'ai_comparison_feedback') and state.ai_comparison_feedback:
                enhancement_context['comparison_feedback'] = state.ai_comparison_feedback
                feedback_sources.append('comparison')
                print("🔄 USING AI COMPARISON FEEDBACK FOR ENHANCEMENT")
                print(f"📋 COMPARISON FEEDBACK RECEIVED FROM ai_statement_comparison_pipeline:")
                print(f"   🔍 Mismatch Explanation: {state.ai_comparison_feedback.get('mismatch_explanation', '')}")
                print(f"   🎯 Expected Output: {state.ai_comparison_feedback.get('expected_output', '')}")
                print(f"   📝 Actual Output: {state.ai_comparison_feedback.get('final_output', '')}")
                print(f"   🔧 Retry Guidance: {state.ai_comparison_feedback.get('retry_guidance', '')}")
                print(f"   🎯 Enhancement Strategy: Address functional differences between pipeline and expected output")
                print(f"   📈 Attempt Number: {getattr(state, 'current_attempt', 1)} (managed by enhancement_iteration_control)")
                # Attempt increment handled by enhancement_iteration_control
                attempt_number = getattr(state, 'current_attempt', 1)
            else:
                # No feedback - first attempt
                print("🆕 FIRST ATTEMPT - No feedback available")
                print(f"📈 Attempt Number: {getattr(state, 'current_attempt', 1)}")
                attempt_number = getattr(state, 'current_attempt', 1)



            # AI Enhancement
            print(f"🤖 Starting AI enhancement of driver module")
            print(f"📝 Input length: {len(enhancement_context['actual_responsible_input'])}")
            print(f"🎯 Expected output length: {len(enhancement_context['expected_final_output'])}")

            enhanced_driver_code = self.ai_enhance_driver_module(driver_module_code, enhancement_context)

            # Save enhanced driver module (overwrites during feedback loops)
            attempt_number = getattr(state, 'current_attempt', 1)
            enhanced_driver_path = self.save_enhanced_driver_module(enhanced_driver_code, attempt_number, state)

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'attempt_number': attempt_number,
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'DRIVER_ENHANCEMENT_COMPLETE',
                    'pipeline_phase': 'Driver Enhancement',
                    'success': True,
                    'notes': f"Enhanced driver using feedback sources: {', '.join(feedback_sources) if feedback_sources else 'None'}. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log module processing for enhancement
                self.log_module_processing(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'attempt_number': attempt_number,
                    'operation': 'DRIVER_ENHANCEMENT',
                    'module_name': 'combined_driver',
                    'module_type': 'responsible',
                    'input_code': driver_module_code,  # Full original driver code
                    'output_code': enhanced_driver_code,  # Full enhanced driver code
                    'status': 'ENHANCED',
                    'changes_made': driver_module_code != enhanced_driver_code,
                    'file_path': enhanced_driver_path,
                    'additional_info': f"AI enhancement using feedback: {', '.join(feedback_sources) if feedback_sources else 'Initial enhancement'}"
                })

                # Log feedback if any was used
                if feedback_sources:
                    feedback_content = ""
                    if 'validation' in feedback_sources and hasattr(state, 'validation_feedback'):
                        feedback_content = str(state.validation_feedback)
                    elif 'execution' in feedback_sources and hasattr(state, 'execution_feedback'):
                        feedback_content = str(state.execution_feedback)
                    elif 'comparison' in feedback_sources and hasattr(state, 'ai_comparison_feedback'):
                        feedback_content = str(state.ai_comparison_feedback)

                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': current_statement_index + 1,
                        'attempt_number': attempt_number,
                        'feedback_type': feedback_sources[0] if feedback_sources else 'none',
                        'source_node': 'enhance_driver_module',
                        'feedback_content': feedback_content,
                        'retry_guidance': 'Applied feedback to enhance driver module',
                        'next_action': 'decompose_enhanced_module',
                        'priority': 'high' if 'validation' in feedback_sources else 'medium'
                    })

            print(f"✅ Driver module enhancement completed")
            print(f"📁 Enhanced driver saved: {enhanced_driver_path}")
            print(f"🔄 Feedback sources used: {', '.join(feedback_sources) if feedback_sources else 'None'}")

            return {
                "enhanced_driver_code": enhanced_driver_code,
                "enhanced_driver_path": enhanced_driver_path,
                "original_driver_code": driver_module_code,
                "feedback_sources": feedback_sources,
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in enhance_driver_module: {str(e)}")
            return {"success": False, "error": str(e)}

    # ==================== DRIVER MODULE HELPER FUNCTIONS ====================

    def save_driver_module(self, driver_code: str, attempt_number: int, state: Stage2WorkflowState) -> str:
        """Save combined driver module with correct naming: driver_{module_names}_attempt_{attempt_number}.py"""
        try:
            # Get feature modules directory
            paths_info = self.setup_module_update_paths(
                state,
                getattr(state, 'current_statement_index', 0) + 1,
                attempt_number
            )
            feature_modules_dir = paths_info['feature_modules_dir']

            # Create directory if needed
            os.makedirs(feature_modules_dir, exist_ok=True)

            # Get responsible module names for filename
            module_categories = getattr(state, 'module_categories', {})
            responsible_modules = module_categories.get('responsible', [])

            if responsible_modules:
                # Create combined name from responsible modules
                module_names = [mod[0] for mod in responsible_modules if len(mod) >= 1]
                combined_name = "_".join(module_names[:3])  # Limit to first 3 modules to avoid long filenames
                if len(module_names) > 3:
                    combined_name += f"_and_{len(module_names)-3}_more"
            else:
                combined_name = "combined"

            # Save driver module with correct naming
            driver_filename = f"driver_{combined_name}_attempt_{attempt_number}.py"
            driver_path = os.path.join(feature_modules_dir, driver_filename)

            with open(driver_path, 'w', encoding='utf-8') as file:
                file.write(driver_code)

            print(f"💾 Saved driver module: {driver_path}")
            return driver_path

        except Exception as e:
            print(f"❌ Error saving driver module: {str(e)}")
            return ""

    def save_enhanced_driver_module(self, enhanced_code: str, attempt_number: int, state: Stage2WorkflowState) -> str:
        """Save enhanced driver module with correct naming: enhanced_{module_names}_attempt_{attempt_number}.py"""
        try:
            # Get feature modules directory
            paths_info = self.setup_module_update_paths(
                state,
                getattr(state, 'current_statement_index', 0) + 1,
                attempt_number
            )
            feature_modules_dir = paths_info['feature_modules_dir']

            # Create directory if needed
            os.makedirs(feature_modules_dir, exist_ok=True)

            # Get responsible module names for filename
            module_categories = getattr(state, 'module_categories', {})
            responsible_modules = module_categories.get('responsible', [])

            if responsible_modules:
                # Create combined name from responsible modules
                module_names = [mod[0] for mod in responsible_modules if len(mod) >= 1]
                combined_name = "_".join(module_names[:3])  # Limit to first 3 modules to avoid long filenames
                if len(module_names) > 3:
                    combined_name += f"_and_{len(module_names)-3}_more"
            else:
                combined_name = "combined"

            # Save enhanced driver module with correct naming (overwrites during feedback)
            enhanced_filename = f"enhanced_{combined_name}_attempt_{attempt_number}.py"
            enhanced_path = os.path.join(feature_modules_dir, enhanced_filename)

            with open(enhanced_path, 'w', encoding='utf-8') as file:
                file.write(enhanced_code)

            print(f"💾 Saved enhanced driver module: {enhanced_path}")
            return enhanced_path

        except Exception as e:
            print(f"❌ Error saving enhanced driver module: {str(e)}")
            return ""

    def ai_enhance_driver_module(self, driver_code: str, enhancement_context: Dict[str, Any]) -> str:
        """AI enhancement of combined driver module."""
        try:
            # Use existing AI enhancement logic but for combined module
            # This leverages the existing AI enhancement infrastructure
            enhanced_code = self.ai_enhance_with_sequential_context(driver_code, enhancement_context)
            return enhanced_code

        except Exception as e:
            print(f"❌ Error in AI driver enhancement: {str(e)}")
            return driver_code  # Return original on error

    # ==================== NEW HIERARCHICAL EXCEL LOGGING SYSTEM ====================

    def log_pipeline_overview(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log high-level pipeline overview to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            excel_data = [{
                "Statement_Number": data.get('statement_number', 0),
                "Attempt_Number": data.get('attempt_number', 1),
                "Timestamp": current_timestamp,
                "Total_Modules": data.get('total_modules', 0),
                "Pre_Count": data.get('pre_count', 0),
                "Responsible_Count": data.get('responsible_count', 0),
                "Post_Count": data.get('post_count', 0),
                "Overall_Status": data.get('overall_status', ''),
                "Final_Output": data.get('final_output', ''),
                "Pipeline_Phase": data.get('pipeline_phase', ''),
                "Success": data.get('success', False),
                "Error_Details": data.get('error_details', ''),
                "Processing_Time": data.get('processing_time', ''),
                "Notes": data.get('notes', '')
            }]

            append_sheet_to_excel(excel_path, "Pipeline_Overview", excel_data)
            print(f"📊 Logged pipeline overview to Pipeline_Overview sheet")

        except Exception as e:
            print(f"❌ Error logging pipeline overview to Excel: {str(e)}")

    def log_module_processing(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log detailed module processing to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            excel_data = [{
                "Statement_Number": data.get('statement_number', 0),
                "Attempt_Number": data.get('attempt_number', 1),
                "Operation": data.get('operation', ''),
                "Module_Name": data.get('module_name', ''),
                "Module_Type": data.get('module_type', ''),  # pre/responsible/post
                "Input_Code": data.get('input_code', ''),  # Full code, no truncation
                "Output_Code": data.get('output_code', ''),  # Full code, no truncation
                "Status": data.get('status', ''),
                "Changes_Made": data.get('changes_made', False),
                "Error_Details": data.get('error_details', ''),
                "File_Path": data.get('file_path', ''),
                "Module_Order": data.get('module_order', 0),
                "Execution_Time": data.get('execution_time', ''),
                "Timestamp": current_timestamp,
                "Additional_Info": data.get('additional_info', '')
            }]

            append_sheet_to_excel(excel_path, "Module_Processing", excel_data)
            print(f"📊 Logged {data.get('operation')} for {data.get('module_name', 'unknown')} to Module_Processing sheet")

        except Exception as e:
            print(f"❌ Error logging module processing to Excel: {str(e)}")

    def log_enhancement_feedback(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log enhancement feedback to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            excel_data = [{
                "Statement_Number": data.get('statement_number', 0),
                "Attempt_Number": data.get('attempt_number', 1),
                "Feedback_Type": data.get('feedback_type', ''),  # validation/execution/comparison
                "Source_Node": data.get('source_node', ''),
                "Feedback_Content": data.get('feedback_content', ''),  # Full feedback, no truncation
                "Retry_Guidance": data.get('retry_guidance', ''),
                "Failed_Modules": data.get('failed_modules', ''),
                "Error_Details": data.get('error_details', ''),
                "Next_Action": data.get('next_action', ''),
                "Priority": data.get('priority', ''),
                "Timestamp": current_timestamp,
                "Additional_Context": data.get('additional_context', '')
            }]

            append_sheet_to_excel(excel_path, "Enhancement_Feedback", excel_data)
            print(f"📊 Logged {data.get('feedback_type')} feedback to Enhancement_Feedback sheet")

        except Exception as e:
            print(f"❌ Error logging enhancement feedback to Excel: {str(e)}")

    def log_developer_summary(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log developer-friendly summary for easy understanding."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            excel_data = [{
                "Statement_Number": data.get('statement_number', 0),
                "Attempt_Number": data.get('attempt_number', 1),
                "Developer_Summary": data.get('developer_summary', ''),
                "What_Happened": data.get('what_happened', ''),
                "Input_Data": data.get('input_data', ''),
                "Output_Data": data.get('output_data', ''),
                "Success_Status": data.get('success_status', ''),
                "Issues_Found": data.get('issues_found', ''),
                "Next_Steps": data.get('next_steps', ''),
                "Technical_Details": data.get('technical_details', ''),
                "File_Locations": data.get('file_locations', ''),
                "Timestamp": current_timestamp
            }]

            append_sheet_to_excel(excel_path, "Developer_Summary", excel_data)
            print(f"📊 Logged developer summary to Developer_Summary sheet")

        except Exception as e:
            print(f"❌ Error logging developer summary to Excel: {str(e)}")

    def decompose_enhanced_module(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 10: Decompose Enhanced Module.

        Purpose: Decompose enhanced driver module back into individual modules
        with functional change detection.
        """
        print("\n" + "="*80)
        print("📦 DECOMPOSE ENHANCED MODULE")
        print("="*80)

        try:
            # Get enhanced driver code and original modules
            enhanced_driver_code = getattr(state, 'enhanced_driver_code', '')
            module_categories = getattr(state, 'module_categories', {})
            responsible_modules = module_categories.get('responsible', [])

            if not enhanced_driver_code:
                return {"success": False, "error": "No enhanced driver code available"}

            # Extract individual modules using boundary markers
            decomposed_modules = []
            attempt_number = getattr(state, 'current_attempt', 1)

            print(f"🔍 Decomposing enhanced driver into {len(responsible_modules)} individual modules")

            for module_data in responsible_modules:
                # Handle different data structures
                if isinstance(module_data, tuple) and len(module_data) >= 2:
                    module_name = module_data[0]
                    original_path = module_data[1]
                elif isinstance(module_data, dict):
                    module_name = module_data.get('module_name', '')
                    original_path = module_data.get('module_path', '')
                else:
                    print(f"⚠️ Unexpected module data format: {module_data}")
                    continue
                # Extract module code between boundary markers
                start_marker = f"# === MODULE: {module_name} START ==="
                end_marker = f"# === MODULE: {module_name} END ==="

                enhanced_module_code = self.extract_module_from_combined(
                    enhanced_driver_code, start_marker, end_marker
                )

                if enhanced_module_code:
                    # Read original module for comparison
                    original_code = self.read_and_decrypt_module(module_name, original_path,
                                                               state.migration_name, state.cloud_category)

                    # Detect functional changes
                    has_functional_changes = self.detect_functional_changes(original_code, enhanced_module_code)

                    # Save individual enhanced module (only if changed)
                    enhanced_module_path = ""
                    if has_functional_changes:
                        enhanced_module_path = self.save_decomposed_module(
                            module_name, enhanced_module_code, attempt_number, state
                        )
                        print(f"   ✅ {module_name}: Functional changes detected - saved as attempt file")
                    else:
                        print(f"   ⚪ {module_name}: No functional changes - using original module")

                    decomposed_modules.append({
                        'module_name': module_name,
                        'original_path': original_path,
                        'enhanced_path': enhanced_module_path,
                        'enhanced_code': enhanced_module_code,
                        'original_code': original_code,
                        'has_functional_changes': has_functional_changes
                    })
                else:
                    print(f"⚠️ Could not extract module {module_name} from enhanced driver")

            # Log to Excel using new hierarchical system
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                modules_with_changes = sum(1 for m in decomposed_modules if m['has_functional_changes'])

                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                    'attempt_number': attempt_number,
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'MODULE_DECOMPOSITION_COMPLETE',
                    'pipeline_phase': 'Module Decomposition',
                    'success': True,
                    'notes': f"Decomposed {len(decomposed_modules)} modules, {modules_with_changes} with functional changes. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log each decomposed module
                for i, module_data in enumerate(decomposed_modules):
                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': attempt_number,
                        'operation': 'MODULE_DECOMPOSITION',
                        'module_name': module_data['module_name'],
                        'module_type': 'responsible',
                        'input_code': 'Extracted from enhanced driver module',
                        'output_code': module_data['enhanced_code'],  # Full decomposed module code
                        'status': 'DECOMPOSED_WITH_CHANGES' if module_data['has_functional_changes'] else 'DECOMPOSED_NO_CHANGES',
                        'changes_made': module_data['has_functional_changes'],
                        'file_path': module_data.get('enhanced_module_path', ''),
                        'module_order': i + 1,
                        'additional_info': f"Original path: {module_data['original_path']}, Functional changes: {module_data['has_functional_changes']}"
                    })

            print(f"✅ Module decomposition completed")
            print(f"📊 {sum(1 for m in decomposed_modules if m['has_functional_changes'])}/{len(decomposed_modules)} modules have functional changes")

            return {
                "decomposed_modules": decomposed_modules,
                "modules_with_changes": sum(1 for m in decomposed_modules if m['has_functional_changes']),
                "decomposition_summary": f"Decomposed {len(decomposed_modules)} modules from driver",
                "success": True
            }

        except Exception as e:
            print(f"❌ Error in decompose_enhanced_module: {str(e)}")
            return {"success": False, "error": str(e)}

    def validate_module_enhancement(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 11: Validate Module Enhancement.

        Purpose: Validate decomposed modules for syntax, boundary markers, and functional changes.
        Provides feedback for enhancement retry if validation fails.
        """
        print("\n" + "="*80)
        print("✅ VALIDATE MODULE ENHANCEMENT")
        print("="*80)

        try:
            # Get decomposed modules
            decomposed_modules = getattr(state, 'decomposed_modules', [])

            if not decomposed_modules:
                print("⚠️ No decomposed modules available for validation")
                return {"success": False, "error": "No decomposed modules to validate"}

            print(f"🔍 Validating {len(decomposed_modules)} decomposed modules")

            validation_results = []
            overall_validation_passed = True

            print(f"🔍 Validating {len(decomposed_modules)} decomposed modules")

            for module_data in decomposed_modules:
                module_name = module_data['module_name']
                enhanced_code = module_data['enhanced_code']
                has_functional_changes = module_data['has_functional_changes']

                # Validation checks
                validation_result = {
                    'module_name': module_name,
                    'has_functional_changes': has_functional_changes,
                    'syntax_valid': True,
                    'has_boundary_markers': True,
                    'validation_passed': True,
                    'validation_errors': []
                }

                # 1. Syntax validation
                try:
                    compile(enhanced_code, f"<{module_name}>", "exec")
                    print(f"   ✅ {module_name}: Syntax validation passed")
                except SyntaxError as e:
                    validation_result['syntax_valid'] = False
                    validation_result['validation_errors'].append(f"Syntax error: {str(e)}")
                    print(f"   ❌ {module_name}: Syntax error - {str(e)}")

                # 2. Boundary marker validation
                start_marker = f"# === MODULE: {module_name} START ==="
                end_marker = f"# === MODULE: {module_name} END ==="

                if start_marker not in enhanced_code or end_marker not in enhanced_code:
                    validation_result['has_boundary_markers'] = False
                    validation_result['validation_errors'].append("Missing boundary markers")
                    print(f"   ❌ {module_name}: Missing boundary markers")
                else:
                    print(f"   ✅ {module_name}: Boundary markers present")

                # 3. Overall validation
                validation_result['validation_passed'] = (
                    validation_result['syntax_valid'] and
                    validation_result['has_boundary_markers']
                )

                if not validation_result['validation_passed']:
                    overall_validation_passed = False

                validation_results.append(validation_result)

            # Prepare validation feedback if failed
            if not overall_validation_passed:
                failed_modules = [r for r in validation_results if not r['validation_passed']]
                validation_feedback = {
                    'failed_modules': [r['module_name'] for r in failed_modules],
                    'validation_errors': [error for r in failed_modules for error in r['validation_errors']],
                    'retry_guidance': "Fix syntax errors and ensure boundary markers are preserved"
                }

                print(f"❌ Validation failed for {len(failed_modules)} modules")
                print(f"🔄 Preparing validation feedback for enhancement retry")
                print(f"📋 VALIDATION FEEDBACK DATA:")
                print(f"   ❌ Failed Modules: {validation_feedback['failed_modules']}")
                print(f"   🐛 Validation Errors: {validation_feedback['validation_errors']}")
                print(f"   🔧 Retry Guidance: {validation_feedback['retry_guidance']}")
                print(f"   📤 This feedback will be passed to enhance_driver_module for next attempt")
                print(f"   🔄 Next Action: enhance_driver_module will use this feedback to fix validation issues")

                # Log validation feedback to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'feedback_type': 'validation',
                        'source_node': 'validate_module_enhancement',
                        'feedback_content': str(validation_feedback),
                        'retry_guidance': validation_feedback['retry_guidance'],
                        'failed_modules': ', '.join(validation_feedback['failed_modules']),
                        'error_details': ', '.join(validation_feedback['validation_errors']),
                        'next_action': 'enhance_driver_module',
                        'priority': 'high'
                    })

            # Pipeline validation logging removed as not required

            print(f"✅ Module validation completed")
            print(f"📊 {sum(1 for r in validation_results if r['validation_passed'])}/{len(validation_results)} modules passed validation")

            # Prepare return data with validation feedback if failed
            return_data = {
                "validation_results": validation_results,
                "validation_passed": overall_validation_passed,
                "modules_validated": len(validation_results),
                "modules_passed": sum(1 for r in validation_results if r['validation_passed']),
                "success": True
            }

            # Add validation feedback to return data if validation failed
            if not overall_validation_passed:
                return_data["validation_feedback"] = validation_feedback
                # Increment validation attempt counter for retry
                return_data["validation_attempt"] = getattr(state, 'validation_attempt', 1) + 1

            return return_data

        except Exception as e:
            print(f"❌ Error in validate_module_enhancement: {str(e)}")
            return {"success": False, "error": str(e)}

    def execute_complete_pipeline(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 12: Execute Complete Pipeline.

        Purpose: Execute complete pipeline (pre + enhanced responsible + post)
        and provide execution feedback if failures occur.
        """
        print("\n" + "="*80)
        print("⚡ EXECUTE COMPLETE PIPELINE")
        print("="*80)

        try:
            # Get pipeline components
            pre_processed_output = getattr(state, 'pre_processed_output', '')
            decomposed_modules = getattr(state, 'decomposed_modules', [])
            module_categories = getattr(state, 'module_categories', {})
            post_execution_modules = module_categories.get('post_execution', [])

            if not pre_processed_output:
                return {"success": False, "error": "No pre-processed output available"}

            # Phase 1: Execute Enhanced Responsible Modules
            current_output = pre_processed_output
            responsible_execution_log = []
            execution_success = True
            execution_errors = []

            print(f"🔍 Phase 1: Executing {len(decomposed_modules)} enhanced responsible modules")
            print(f"📝 Starting with pre-processed input: {current_output[:100]}...")

            for module_data in decomposed_modules:
                if module_data.get('has_functional_changes', False):
                    # Use enhanced module
                    module_name = module_data['module_name']
                    enhanced_code = module_data['enhanced_code']

                    try:
                        previous_output = current_output
                        print(f"🔍 Executing enhanced module: {module_name}")

                        # Execute enhanced module
                        current_output = self.execute_enhanced_module(current_output, enhanced_code, state.schema_name)

                        # Log transformation
                        transformation_applied = current_output != previous_output
                        responsible_execution_log.append({
                            'module_name': module_name,
                            'module_type': 'enhanced',
                            'transformation_applied': transformation_applied,
                            'input_length': len(previous_output),
                            'output_length': len(current_output)
                        })

                        print(f"   ✅ {module_name}: {'Applied transformation' if transformation_applied else 'No changes'}")

                    except Exception as e:
                        execution_success = False
                        error_msg = f"Enhanced module {module_name} execution failed: {str(e)}"
                        execution_errors.append(error_msg)
                        print(f"   ❌ {module_name}: Execution error - {str(e)}")
                else:
                    # Use original module
                    module_name = module_data['module_name']
                    original_path = module_data['original_path']

                    try:
                        original_code = self.read_and_decrypt_module(module_name, original_path,
                                                                   state.migration_name, state.cloud_category)
                        if original_code:
                            previous_output = current_output
                            print(f"🔍 Executing original module: {module_name}")

                            current_output = self.apply_feature_module(current_output, module_name, original_code, state)

                            transformation_applied = current_output != previous_output
                            responsible_execution_log.append({
                                'module_name': module_name,
                                'module_type': 'original',
                                'transformation_applied': transformation_applied,
                                'input_length': len(previous_output),
                                'output_length': len(current_output)
                            })

                            print(f"   ✅ {module_name}: {'Applied transformation' if transformation_applied else 'No changes'}")

                    except Exception as e:
                        execution_success = False
                        error_msg = f"Original module {module_name} execution failed: {str(e)}"
                        execution_errors.append(error_msg)
                        print(f"   ❌ {module_name}: Execution error - {str(e)}")

            responsible_output = current_output

            # Phase 2: Execute Post-Processing Modules
            post_execution_log = []

            print(f"🔍 Phase 2: Executing {len(post_execution_modules)} post-processing modules")

            for module_name, module_path in post_execution_modules:
                try:
                    module_code = self.read_and_decrypt_module(module_name, module_path,
                                                             state.migration_name, state.cloud_category)
                    if module_code:
                        previous_output = current_output
                        print(f"🔍 Executing post-module: {module_name}")

                        current_output = self.apply_feature_module(current_output, module_name, module_code, state)

                        transformation_applied = current_output != previous_output
                        post_execution_log.append({
                            'module_name': module_name,
                            'module_type': 'post',
                            'transformation_applied': transformation_applied,
                            'input_length': len(previous_output),
                            'output_length': len(current_output)
                        })

                        print(f"   ✅ {module_name}: {'Applied transformation' if transformation_applied else 'No changes'}")

                except Exception as e:
                    execution_success = False
                    error_msg = f"Post module {module_name} execution failed: {str(e)}"
                    execution_errors.append(error_msg)
                    print(f"   ❌ {module_name}: Execution error - {str(e)}")

            # Replace comment markers with original comments before finalizing output
            comments_dict = getattr(state, 'comments_dict', {})
            if comments_dict:
                print("🔄 Replacing comment markers with original comments...")
                current_output = replace_comment_markers(current_output, comments_dict)
                print(f"✅ Comment markers replaced successfully")
            else:
                print("ℹ️ No comments dictionary found, skipping comment replacement")

            final_output = current_output

            # Prepare execution feedback if failed
            if not execution_success:
                execution_feedback = {
                    'execution_errors': execution_errors,
                    'failed_phase': 'responsible' if any('Enhanced module' in err or 'Original module' in err for err in execution_errors) else 'post',
                    'retry_guidance': "Fix runtime errors in enhanced modules"
                }

                print(f"❌ Pipeline execution failed - preparing execution feedback")
                print(f"📋 EXECUTION FEEDBACK DATA:")
                print(f"   💥 Execution Errors: {execution_feedback['execution_errors']}")
                print(f"   📍 Failed Phase: {execution_feedback['failed_phase']}")
                print(f"   🔧 Retry Guidance: {execution_feedback['retry_guidance']}")
                print(f"   📤 This feedback will be passed to enhance_driver_module for next attempt")
                print(f"   🔄 Next Action: enhance_driver_module will use this feedback to fix runtime errors")

                # Log execution feedback to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'feedback_type': 'execution',
                        'source_node': 'execute_complete_pipeline',
                        'feedback_content': str(execution_feedback),
                        'retry_guidance': execution_feedback['retry_guidance'],
                        'failed_modules': execution_feedback['failed_phase'],
                        'error_details': ', '.join(execution_feedback['execution_errors']),
                        'next_action': 'enhance_driver_module',
                        'priority': 'medium'
                    })

                print(f"❌ Pipeline execution failed with {len(execution_errors)} errors")
                print(f"🔄 Preparing execution feedback for enhancement retry")

            # Log to Excel
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Pipeline validation logging removed as not required

                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Log pipeline overview update with correct data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'PIPELINE_EXECUTION_COMPLETE',
                    'pipeline_phase': 'Complete Pipeline Execution',
                    'success': execution_success,
                    'final_output': final_output,
                    'notes': f"Executed {len(decomposed_modules)} enhanced responsible + {len(post_execution_modules)} post modules. Status: {'SUCCESS' if execution_success else 'FAILED'}. Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log post-execution modules to new hierarchical system
                for i, log_entry in enumerate(post_execution_log):
                    self.log_module_processing(excel_path, {
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'operation': 'POST_EXECUTION',
                        'module_name': log_entry['module_name'],
                        'module_type': 'post',
                        'input_code': f"Input length: {log_entry['input_length']} chars",
                        'output_code': f"Output length: {log_entry['output_length']} chars",
                        'status': 'EXECUTED',
                        'changes_made': log_entry['transformation_applied'],
                        'module_order': i + 1,
                        'additional_info': f"Transformation applied: {log_entry['transformation_applied']}"
                    })

            print(f"✅ Complete pipeline execution completed")
            print(f"📤 Final output: {final_output[:100]}...")
            print(f"📊 Execution status: {'SUCCESS' if execution_success else 'FAILED'}")

            # Prepare return data with execution feedback if failed
            return_data = {
                "final_output": final_output,
                "responsible_output": responsible_output,
                "execution_success": execution_success,
                "execution_errors": execution_errors,
                "responsible_execution_log": responsible_execution_log,
                "post_execution_log": post_execution_log,
                "pipeline_summary": f"Executed {len(decomposed_modules)} enhanced + {len(post_execution_modules)} post modules",
                "success": True
            }

            # Add execution feedback to return data if execution failed
            if not execution_success:
                return_data["execution_feedback"] = execution_feedback
                # Increment execution attempt counter for retry
                return_data["execution_attempt"] = getattr(state, 'execution_attempt', 1) + 1

            return return_data

        except Exception as e:
            print(f"❌ Error in execute_complete_pipeline: {str(e)}")
            return {"success": False, "error": str(e)}

    def ai_statement_comparison_pipeline(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 13: AI Statement Comparison Pipeline - Functional Equivalence Analysis

        Purpose:
            Performs sophisticated AI-driven comparison between the final pipeline output
            and the expected AI converted statement to determine functional equivalence.
            This is the final validation step that determines if enhancement was successful.

        Business Logic:
            1. Compares final_output (from execute_complete_pipeline) vs ai_converted_statement
            2. Uses AI to analyze functional equivalence beyond simple text matching
            3. Ignores formatting differences (whitespace, case, comments)
            4. Focuses on logical and functional equivalence
            5. Provides detailed comparison feedback for failed attempts

        AI Comparison Process:
            - Normalizes both statements for comparison (removes formatting differences)
            - Analyzes SQL logic, function calls, data transformations
            - Identifies functional differences vs cosmetic differences
            - Provides specific guidance for fixing functional discrepancies
            - Considers database-specific syntax variations

        Comparison Criteria:
            - Functional logic equivalence
            - Data transformation accuracy
            - SQL syntax correctness
            - Database function usage
            - Parameter handling
            - Error handling logic

        Input Requirements:
            - state.final_output: Final pipeline execution result
            - state.ai_converted_statement: Expected conversion target
            - state.original_source_statement: Original source for context
            - state.migration_name: For database-specific analysis
            - state.max_attempts: Maximum retry attempts allowed

        Output:
            - comparison_result: Boolean indicating functional equivalence
            - comparison_feedback: Detailed AI analysis of differences
            - statements_match: Simple boolean for workflow routing
            - comparison_details: Technical comparison breakdown

        Next Nodes:
            - Success (match) → more_statements_decision
            - Failure (no match) → enhancement_iteration_control (retry)

        Error Handling:
            - AI comparison failures with fallback analysis
            - Missing statement data validation
            - Comparison timeout handling
        """
        print("\n" + "="*80)
        print("🧠 AI STATEMENT COMPARISON PIPELINE")
        print("="*80)

        try:
            # Get comparison inputs
            final_output = getattr(state, 'final_output', '')
            current_statement_index = getattr(state, 'current_statement_index', 0)
            available_features_with_statements = getattr(state, 'available_features_with_statements', [])
            current_statement = available_features_with_statements[current_statement_index]
            expected_output = current_statement.get('ai_converted_statement', '')

            if not final_output or not expected_output:
                return {"success": False, "error": "Missing comparison inputs"}

            # Prepare comparison data for ai_compare_statement_functionality
            statement_data = {
                'updated_ai_converted_statement': final_output,  # Our pipeline output
                'ai_converted_statement': expected_output        # Expected correct output from CSV
            }

            print(f"🔍 Comparing pipeline output vs expected AI output")
            print(f"📝 PIPELINE OUTPUT ({len(final_output)} chars):")
            print(f"{'='*80}")
            print(final_output)
            print(f"{'='*80}")

            print(f"🎯 EXPECTED OUTPUT ({len(expected_output)} chars):")
            print(f"{'='*80}")
            print(expected_output)
            print(f"{'='*80}")

            # Debug: Check if either is empty
            if not final_output:
                print("❌ DEBUG: final_output is empty!")
            if not expected_output:
                print("❌ DEBUG: expected_output (ai_converted_statement) is empty!")
                print(f"❌ DEBUG: current_statement keys: {list(current_statement.keys())}")
                print(f"❌ DEBUG: current_statement_index: {current_statement_index}")
                print(f"❌ DEBUG: total statements: {len(available_features_with_statements)}")

            # AI Comparison using existing logic
            comparison_result = self.ai_compare_statement_functionality(statement_data, state.migration_name)

            statements_match = comparison_result.get('statements_match', False)
            explanation = comparison_result.get('explanation', '')
            transformation_guidance = comparison_result.get('transformation_guidance', {})

            # Prepare comparison feedback if failed
            if not statements_match:
                comparison_feedback = {
                    'mismatch_explanation': explanation,
                    'final_output': final_output,
                    'expected_output': expected_output,
                    'retry_guidance': "Address functional differences between pipeline output and expected output",
                    'transformation_guidance': transformation_guidance
                }

                print(f"❌ AI comparison failed - statements don't match")
                print(f"🔄 Preparing comparison feedback for enhancement retry")
                print(f"📋 AI COMPARISON FEEDBACK DATA:")
                print(f"   🔍 Mismatch Explanation: {comparison_feedback['mismatch_explanation']}")
                print(f"   📝 Pipeline Output: {comparison_feedback['final_output']}")
                print(f"   🎯 Expected Output: {comparison_feedback['expected_output']}")
                print(f"   🔧 Retry Guidance: {comparison_feedback['retry_guidance']}")

                # Show transformation guidance if available
                if transformation_guidance and transformation_guidance.get('required'):
                    print(f"   🛠️ Transformation Guidance Available:")
                    specific_changes = transformation_guidance.get('specific_changes', [])
                    for i, change in enumerate(specific_changes, 1):
                        print(f"      {i}. {change.get('source_pattern', '')} → {change.get('target_pattern', '')}")
                    implementation_steps = transformation_guidance.get('implementation_steps', [])
                    if implementation_steps:
                        print(f"   📋 Implementation Steps: {len(implementation_steps)} steps provided")

                print(f"   📤 This feedback will be passed to enhance_driver_module for next attempt")

            # Log to Excel with comprehensive developer information
            excel_path = getattr(state, 'stage2_excel_path', None)
            if excel_path:
                # Get module counts from state for consistent data
                module_categories = getattr(state, 'module_categories', {})
                pre_count = len(module_categories.get('pre_execution', []))
                responsible_count = len(module_categories.get('responsible', []))
                post_count = len(module_categories.get('post_execution', []))
                total_modules = pre_count + responsible_count + post_count

                # Get post features data from current statement
                current_statement_data = available_features_with_statements[current_statement_index]
                post_features = current_statement_data.get('post_features', [])
                post_features_str = ', '.join([f[0] if isinstance(f, tuple) else str(f) for f in post_features[:3]])
                if len(post_features) > 3:
                    post_features_str += f" and {len(post_features)-3} more"

                # Log to pipeline overview with complete data
                self.log_pipeline_overview(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'total_modules': total_modules,
                    'pre_count': pre_count,
                    'responsible_count': responsible_count,
                    'post_count': post_count,
                    'overall_status': 'AI_COMPARISON_COMPLETE',
                    'pipeline_phase': 'AI Statement Comparison',
                    'success': statements_match,
                    'final_output': final_output,
                    'notes': f"Comparison result: {'MATCH' if statements_match else 'NO MATCH'} - {explanation[:100]}... | Post features: {post_features_str} | Total pipeline: {pre_count} pre + {responsible_count} responsible + {post_count} post = {total_modules} modules"
                })

                # Log detailed comparison for developers
                self.log_developer_summary(excel_path, {
                    'statement_number': current_statement_index + 1,
                    'attempt_number': getattr(state, 'current_attempt', 1),
                    'developer_summary': f"AI Statement Comparison: {'SUCCESS' if statements_match else 'FAILED'}",
                    'what_happened': f"Compared final pipeline output against expected AI converted statement. Result: {'MATCH' if statements_match else 'NO MATCH'}",
                    'input_data': f"Pipeline Output: {final_output}",
                    'output_data': f"Expected Output: {expected_output}",
                    'success_status': 'SUCCESS' if statements_match else 'FAILED',
                    'issues_found': explanation if not statements_match else 'No issues - statements match functionally',
                    'next_steps': 'Proceed to next statement' if statements_match else 'Retry enhancement with comparison feedback',
                    'technical_details': f"AI Analysis: {explanation}",
                    'file_locations': f"Statement {current_statement_index + 1} processing"
                })

                # Log comparison feedback if failed
                if not statements_match:
                    self.log_enhancement_feedback(excel_path, {
                        'statement_number': current_statement_index + 1,
                        'attempt_number': getattr(state, 'current_attempt', 1),
                        'feedback_type': 'comparison',
                        'source_node': 'ai_statement_comparison_pipeline',
                        'feedback_content': str(comparison_feedback),
                        'retry_guidance': comparison_feedback['retry_guidance'],
                        'error_details': explanation,
                        'next_action': 'enhance_driver_module',
                        'priority': 'low',
                        'additional_context': f"Pipeline: {final_output[:200]}... | Expected: {expected_output[:200]}..."
                    })

            print(f"✅ AI statement comparison completed")
            print(f"📊 Comparison result: {'MATCH' if statements_match else 'NO MATCH'}")
            if not statements_match:
                print(f"📝 Explanation: {explanation[:200]}...")

            # Prepare return data with comparison feedback if failed
            return_data = {
                "statements_match": statements_match,
                "comparison_result": comparison_result,
                "explanation": explanation,
                "final_output": final_output,
                "expected_output": expected_output,
                "success": True
            }

            # Add comparison feedback to return data if comparison failed
            if not statements_match:
                return_data["ai_comparison_feedback"] = comparison_feedback

            return return_data

        except Exception as e:
            print(f"❌ Error in ai_statement_comparison_pipeline: {str(e)}")
            return {"success": False, "error": str(e)}

    def enhancement_iteration_control(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """
        Node 14: Enhancement Iteration Control.

        Purpose: Control iteration logic based on AI comparison results and attempt limits.
        Manages proceed/retry/fail decisions with attempt tracking.
        """
        print("\n" + "="*80)
        print("🔄 ENHANCEMENT ITERATION CONTROL")
        print("="*80)

        try:
            # Get iteration control inputs
            statements_match = getattr(state, 'statements_match', False)
            current_attempt = getattr(state, 'current_attempt', 1)
            max_attempts = getattr(state, 'max_attempts', 5)

            print(f"🔍 Iteration control analysis:")
            print(f"   📊 Statements match: {statements_match}")
            print(f"   🔢 Current attempt: {current_attempt}/{max_attempts}")

            # Decision Logic
            if statements_match:
                # SUCCESS - Proceed to next statement
                iteration_action = "proceed"
                message = f'Statement enhancement successful after {current_attempt} attempts'

                print(f"✅ {message}")

                # Log success to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    self.log_ai_analysis_control_to_excel(excel_path, {
                        'operation': 'ITERATION_CONTROL',
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': current_attempt,
                        'iteration_action': iteration_action,
                        'decision_reason': 'Statements match - enhancement successful',
                        'message': message
                    })

                return {
                    "iteration_action": iteration_action,
                    "success": True,
                    "message": message
                }

            elif current_attempt >= max_attempts:
                # FAILURE - Max attempts reached
                iteration_action = "fail"
                message = f'Statement enhancement failed after {max_attempts} attempts'

                print(f"❌ {message}")

                # Log failure to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    self.log_ai_analysis_control_to_excel(excel_path, {
                        'operation': 'ITERATION_CONTROL',
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': current_attempt,
                        'iteration_action': iteration_action,
                        'decision_reason': 'Max attempts reached - marking as failed',
                        'message': message
                    })

                return {
                    "iteration_action": iteration_action,
                    "success": False,
                    "message": message,
                    "final_attempt_number": current_attempt
                }

            else:
                # RETRY - Increment attempt and retry enhancement
                next_attempt = current_attempt + 1
                iteration_action = "retry"
                message = f'Retrying enhancement - attempt {next_attempt}/{max_attempts}'

                print(f"🔄 {message}")

                # Aggregate feedback from all sources for next attempt
                aggregated_feedback = self.aggregate_enhancement_feedback(state)

                # Log retry to Excel
                excel_path = getattr(state, 'stage2_excel_path', None)
                if excel_path:
                    self.log_ai_analysis_control_to_excel(excel_path, {
                        'operation': 'ITERATION_CONTROL',
                        'statement_number': getattr(state, 'current_statement_index', 0) + 1,
                        'attempt_number': current_attempt,
                        'iteration_action': iteration_action,
                        'decision_reason': f'Statements do not match - retrying attempt {next_attempt}',
                        'message': message,
                        'next_attempt_number': next_attempt,
                        'aggregated_feedback': str(aggregated_feedback)
                    })

                return {
                    "iteration_action": iteration_action,
                    "success": False,
                    "next_attempt_number": next_attempt,
                    "aggregated_feedback": aggregated_feedback,
                    "message": message,
                    "current_attempt": next_attempt,  # Update attempt number
                    "enhancement_feedback_for_retry": aggregated_feedback  # Add feedback for retry
                }

        except Exception as e:
            print(f"❌ Error in enhancement_iteration_control: {str(e)}")
            return {"success": False, "error": str(e)}

    # ==================== ADDITIONAL HELPER FUNCTIONS ====================

    def extract_module_from_combined(self, combined_code: str, start_marker: str, end_marker: str) -> str:
        """Extract individual module code from combined driver module using boundary markers."""
        try:
            start_index = combined_code.find(start_marker)
            end_index = combined_code.find(end_marker)

            if start_index == -1 or end_index == -1:
                return ""

            # Extract code including markers for validation
            end_index += len(end_marker)
            module_code = combined_code[start_index:end_index].strip()

            return module_code

        except Exception as e:
            print(f"❌ Error extracting module from combined code: {str(e)}")
            return ""

    def detect_functional_changes(self, original_code: str, enhanced_code: str) -> bool:
        """Detect if there are functional changes between original and enhanced code."""
        try:
            # Normalize code for comparison (remove comments, whitespace differences)
            original_normalized = self.normalize_code_for_comparison(original_code)
            enhanced_normalized = self.normalize_code_for_comparison(enhanced_code)

            return original_normalized != enhanced_normalized

        except Exception as e:
            print(f"❌ Error detecting functional changes: {str(e)}")
            return True  # Assume changes on error

    def normalize_code_for_comparison(self, code: str) -> str:
        """Normalize code by removing comments and normalizing whitespace."""
        try:
            lines = []
            for line in code.split('\n'):
                # Remove comments and strip whitespace
                line = line.split('#')[0].strip()
                if line:  # Only keep non-empty lines
                    lines.append(line)

            return '\n'.join(lines)

        except Exception as e:
            print(f"❌ Error normalizing code: {str(e)}")
            return code

    def save_decomposed_module(self, module_name: str, module_code: str, attempt_number: int, state: Stage2WorkflowState) -> str:
        """Save individual decomposed module (only if functionally changed)."""
        try:
            # Get feature modules directory
            paths_info = self.setup_module_update_paths(
                state,
                getattr(state, 'current_statement_index', 0) + 1,
                attempt_number
            )
            feature_modules_dir = paths_info['feature_modules_dir']

            # Create directory if needed
            os.makedirs(feature_modules_dir, exist_ok=True)

            # Save decomposed module with attempt-based naming
            module_filename = f"{module_name}_attempt_{attempt_number}.py"
            module_path = os.path.join(feature_modules_dir, module_filename)

            with open(module_path, 'w', encoding='utf-8') as file:
                file.write(module_code)

            print(f"💾 Saved decomposed module: {module_path}")
            return module_path

        except Exception as e:
            print(f"❌ Error saving decomposed module: {str(e)}")
            return ""

    def execute_enhanced_module(self, statement: str, module_code: str, schema_name: str) -> str:
        """Execute enhanced module code using importlib.util."""
        try:
            # Use existing module execution logic
            return self.apply_feature_module(statement, "enhanced_module", module_code,
                                           type('State', (), {'schema_name': schema_name})())

        except Exception as e:
            print(f"❌ Error executing enhanced module: {str(e)}")
            return statement  # Return original on error

    def aggregate_enhancement_feedback(self, state: Stage2WorkflowState) -> Dict[str, Any]:
        """Combine feedback from validation, execution, and comparison for next enhancement attempt."""
        feedback = {
            'attempt_number': getattr(state, 'current_attempt', 1),
            'feedback_sources': []
        }

        # Validation feedback (highest priority)
        if hasattr(state, 'validation_feedback') and state.validation_feedback:
            feedback['validation_issues'] = state.validation_feedback
            feedback['feedback_sources'].append('validation')

        # Execution feedback (medium priority)
        if hasattr(state, 'execution_feedback') and state.execution_feedback:
            feedback['execution_issues'] = state.execution_feedback
            feedback['feedback_sources'].append('execution')

        # Comparison feedback (lowest priority)
        if hasattr(state, 'ai_comparison_feedback') and state.ai_comparison_feedback:
            feedback['comparison_issues'] = state.ai_comparison_feedback
            feedback['feedback_sources'].append('comparison')

        return feedback

    def log_ai_analysis_control_to_excel(self, excel_path: str, data: Dict[str, Any]) -> None:
        """Log AI analysis and control results (comparison + iteration) to Excel."""
        try:
            current_timestamp = pd.Timestamp.now().strftime("%Y-%m-%d %H:%M:%S")

            excel_data = [{
                "Statement_Number": data.get('statement_number', 0),
                "Attempt_Number": data.get('attempt_number', 1),
                "Operation": data.get('operation', ''),
                "Result": data.get('statements_match', data.get('iteration_action', '')),
                "Decision_Reason": data.get('decision_reason', data.get('explanation', '')),
                "Final_Output": data.get('final_output', '')[:200] if data.get('final_output') else '',
                "Expected_Output": data.get('expected_output', '')[:200] if data.get('expected_output') else '',
                "Next_Attempt": data.get('next_attempt_number', ''),
                "Message": data.get('message', ''),
                "Feedback": str(data.get('aggregated_feedback', '')),
                "Timestamp": current_timestamp
            }]

            append_sheet_to_excel(excel_path, "AI_Analysis_Control", excel_data)
            print(f"📊 Logged {data.get('operation')} to AI_Analysis_Control sheet")

        except Exception as e:
            print(f"❌ Error logging AI analysis control to Excel: {str(e)}")